import { liveStreamingApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  streamingData: null,
  isStreaming: false,
  streamToken: null,
  roomName: null,
  error: null,
  chatMessages: [],
  chatLoading: false,
  chatError: null,
  // Quiz states
  quizData: null,
  isQuizActive: false,
  currentQuestionIndex: 0,
  quizObjectId: null,
  questionTimer: 10,
  quizResults: [],
  isQuizLoading: false,
  quizError: null
};

export const teacherLiveStreamingSlice = liveStreamingApi.injectEndpoints({
  endpoints: (builder) => ({
    startEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/start',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Start Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    stopEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/stop',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Stop Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    sendChatMessage: builder.mutation({
      query: (body) => ({
        url: '/api/chat/send',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveStreaming']
    }),
    getChatHistory: builder.query({
      query: (sessionId) => `/api/chat/history/${sessionId}`,
      transformResponse: (response) => {
        console.log('Chat History Response:', response);
        return response.messages || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),

    // Quiz endpoints
    uploadQuizPdf: builder.mutation({
      query: (formData) => ({
        url: 'https://sasthra.in/upload_cbt_paper',
        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Upload Quiz PDF Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),

    startQuiz: builder.mutation({
      query: (body) => ({
        url: 'https://sasthra.in/content/start_quiz',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Start Quiz Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),

    nextQuestion: builder.mutation({
      query: (body) => ({
        url: 'https://sasthra.in/content/next_question',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Next Question Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

const LiveStreamingSlice = createSlice({
  name: 'liveStreaming',
  initialState,
  reducers: {
    setStreamingData: (state, action) => {
      state.streamingData = action.payload;
    },
    setIsStreaming: (state, action) => {
      state.isStreaming = action.payload;
    },
    setStreamToken: (state, action) => {
      state.streamToken = action.payload;
    },
    setRoomName: (state, action) => {
      state.roomName = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setChatMessages: (state, action) => {
      state.chatMessages = action.payload;
    },
    addChatMessage: (state, action) => {
      state.chatMessages.push(action.payload);
    },
    setChatLoading: (state, action) => {
      state.chatLoading = action.payload;
    },
    setChatError: (state, action) => {
      state.chatError = action.payload;
    },
    clearStreamingData: (state) => {
      state.streamingData = null;
      state.isStreaming = false;
      state.streamToken = null;
      state.roomName = null;
      state.error = null;
      state.chatMessages = [];
      state.chatLoading = false;
      state.chatError = null;
      // Clear quiz data
      state.quizData = null;
      state.isQuizActive = false;
      state.currentQuestionIndex = 0;
      state.quizObjectId = null;
      state.questionTimer = 10;
      state.quizResults = [];
      state.isQuizLoading = false;
      state.quizError = null;
    },
    // Quiz reducers
    setQuizData: (state, action) => {
      state.quizData = action.payload;
    },
    setQuizObjectId: (state, action) => {
      state.quizObjectId = action.payload;
    },
    setIsQuizActive: (state, action) => {
      state.isQuizActive = action.payload;
    },
    setCurrentQuestionIndex: (state, action) => {
      state.currentQuestionIndex = action.payload;
    },
    setQuestionTimer: (state, action) => {
      state.questionTimer = action.payload;
    },
    setQuizResults: (state, action) => {
      state.quizResults = action.payload;
    },
    addQuizResult: (state, action) => {
      state.quizResults.push(action.payload);
    },
    setIsQuizLoading: (state, action) => {
      state.isQuizLoading = action.payload;
    },
    setQuizError: (state, action) => {
      state.quizError = action.payload;
    },
    nextQuizQuestion: (state) => {
      if (state.quizData && state.currentQuestionIndex < state.quizData.questions.length - 1) {
        state.currentQuestionIndex += 1;
        state.questionTimer = 10;
      }
    },
    resetQuiz: (state) => {
      state.quizData = null;
      state.isQuizActive = false;
      state.currentQuestionIndex = 0;
      state.quizObjectId = null;
      state.questionTimer = 10;
      state.quizResults = [];
      state.isQuizLoading = false;
      state.quizError = null;
    }
  }
});

export const {
  setStreamingData,
  setIsStreaming,
  setStreamToken,
  setRoomName,
  setError,
  setChatMessages,
  addChatMessage,
  setChatLoading,
  setChatError,
  clearStreamingData,
  // Quiz actions
  setQuizData,
  setQuizObjectId,
  setIsQuizActive,
  setCurrentQuestionIndex,
  setQuestionTimer,
  setQuizResults,
  addQuizResult,
  setIsQuizLoading,
  setQuizError,
  nextQuizQuestion,
  resetQuiz
} = LiveStreamingSlice.actions;

export default LiveStreamingSlice.reducer;

export const {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useGetChatHistoryQuery,
  useLazyGetChatHistoryQuery,
  // Quiz hooks
  useUploadQuizPdfMutation,
  useStartQuizMutation,
  useNextQuestionMutation
} = teacherLiveStreamingSlice;