import React, { useEffect, useRef, useState } from 'react';
import io from 'socket.io-client';
import { X } from 'lucide-react';

const FLASK_API_URL = 'https://sasthra.in';
const SOCKETIO_URL = 'https://sasthra.in';
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 10;

const ContentQuiz = ({ processId, onClose }) => {
  const [quizId, setQuizId] = useState(null);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [summary, setSummary] = useState(null);
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [debugLogs, setDebugLogs] = useState([]);
  const [error, setError] = useState(null);
  const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);

  const addDebugLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[DEBUG] ${message}`);
  };

  useEffect(() => {
    if (quizId && videoRef.current) {
      initCamera();
    }
  }, [quizId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  const startQuiz = async () => {
    if (!processId || !centerCode) {
      setError('Missing ProcessId or CenterCode');
      return;
    }
    
    try {
      addDebugLog('Starting quiz...');
      const res = await fetch(`${FLASK_API_URL}/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ process_selector_id: processId, center_code: centerCode })
      });
      
      const data = await res.json();
      if (!res.ok || data.error) throw new Error(data.error || `Server error ${res.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start capture for this question.');
      addDebugLog(`Quiz started with ID: ${data.quiz_id}`);
      
      // Setup socket connection after quiz starts
      setupSocketIO();
    } catch (error) {
      addDebugLog(`Error starting quiz: ${error.message}`);
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  const handleNextQuestion = async () => {
    if (!quizId) return;
    
    try {
      addDebugLog('Starting capture cycle...');
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');

      const res = await fetch(`${FLASK_API_URL}/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, process_selector_id: processId })
      });
      
      const data = await res.json();

      if (data.summary_of_last_question) setSummary(data.summary_of_last_question);
      if (data.overall_summary) {
        setFinalResults(data);
        addDebugLog('Quiz completed!');
        stopCamera();
        socketRef.current?.disconnect();
      } else if (data.question) {
        setQuestionData(data);
        setQuizStatus('Ready to start capture for this question.');
        addDebugLog(`Loaded question ${data.question_number}`);
      }
    } catch (error) {
      addDebugLog(`Error during quiz progression: ${error.message}`);
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
    }
  };

  const startCaptureCycle = async () => {
    const options = ['a', 'b', 'c', 'd'];
    addDebugLog('Starting capture cycle for all options...');
    
    for (let i = 0; i < options.length; i++) {
      addDebugLog(`Processing option ${options[i].toUpperCase()}...`);
      setQuizStatus(`Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`);
      await processSingleOption(options[i]);
    }
    
    setQuizStatus('Capture complete for this question. Results sent.');
    addDebugLog('Capture cycle completed for all options');
    
    // Reset timer display
    setCurrentOptionTimer(null);
    setCurrentOption('');
  };

  const processSingleOption = (optionChar) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let frameCount = 0;
      
      // Set current option and start timer
      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);
      
      // Update timer every second
      timerIntervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
        setCurrentOptionTimer(remaining);
      }, 1000);
      
      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);

      setTimeout(() => {
        clearInterval(intervalId);
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
        }
        addDebugLog(`Option ${optionChar.toUpperCase()} complete - sent ${frameCount} frames`);
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };

  const captureAndSendFrame = (optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (!video || !canvas) {
      addDebugLog('Video or canvas not available');
      return;
    }
    
    if (!socketRef.current?.connected) {
      addDebugLog('Socket not connected, cannot send frame');
      return;
    }

    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);

    socketRef.current.emit('process_frame', {
      quiz_id: quizId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    });
  };

  const setupSocketIO = () => {
    if (socketRef.current) {
      addDebugLog('Disconnecting existing socket...');
      socketRef.current.disconnect();
    }
    
    addDebugLog(`Connecting to socket at ${SOCKETIO_URL}/socketio1/socket.io`);
    
    socketRef.current = io(SOCKETIO_URL, {
      path: '/socketio1/socket.io',
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5
    });

    socketRef.current.on('connect', () => {
      addDebugLog('✅ Connected to Socket.IO');
      setSocketStatus('Connected');
    });

    socketRef.current.on('disconnect', (reason) => {
      addDebugLog(`❌ Disconnected from Socket.IO: ${reason}`);
      setSocketStatus('Disconnected');
    });

    socketRef.current.on('connect_error', (error) => {
      addDebugLog(`❌ Connection error: ${error.message}`);
      setSocketStatus('Connection Error');
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      addDebugLog(`🔄 Reconnected after ${attemptNumber} attempts`);
      setSocketStatus('Reconnected');
    });

    socketRef.current.on('reconnect_error', (error) => {
      addDebugLog(`🔄 Reconnection error: ${error.message}`);
    });

    socketRef.current.on('reconnect_failed', () => {
      addDebugLog('🔄 Reconnection failed');
      setSocketStatus('Reconnection Failed');
    });

    socketRef.current.on('hand_raised', (data) => {
      addDebugLog(`👋 Hand raised event received: ${JSON.stringify(data)}`);
      setLiveFeedLogs((logs) => [...logs, data]);
    });

    socketRef.current.onAny((eventName, ...args) => {
      addDebugLog(`📨 Received event '${eventName}': ${JSON.stringify(args)}`);
    });
  };

  const initCamera = async () => {
    if (videoStreamRef.current) return;
    
    try {
      addDebugLog('Initializing camera...');
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: { ideal: 1280 }, 
          height: { ideal: 720 } 
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
        addDebugLog('✅ Camera initialized successfully');
      }
    } catch (error) {
      addDebugLog(`❌ Camera initialization failed: ${error.message}`);
      setError(`Camera error: ${error.message}`);
    }
  };

  const stopCamera = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
      addDebugLog('Camera stopped');
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const clearDebugLogs = () => {
    setDebugLogs([]);
    setLiveFeedLogs([]);
  };

  const testSocketConnection = () => {
    if (socketRef.current) {
      addDebugLog('Testing socket connection...');
      socketRef.current.emit('test_connection', { message: 'Hello from client' });
    } else {
      addDebugLog('No socket connection to test');
    }
  };

  // Auto-start quiz if processId and centerCode are available
  useEffect(() => {
    if (processId && centerCode) {
      startQuiz();
    }
  }, [processId, centerCode]);

  return (
    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">AI Tutor Quiz Tester</h2>
        <button onClick={onClose} className="text-white hover:text-blue-400">
          <X size={24} />
        </button>
      </div>

      {/* Socket Status */}
      <div className={`p-4 rounded-lg mb-6 ${
        socketStatus === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        <strong>Socket Status:</strong> {socketStatus}
        {socketRef.current && (
          <button 
            onClick={testSocketConnection}
            className="ml-4 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Test Connection
          </button>
        )}
      </div>

      {/* Start Section (shown only if quiz hasn't started) */}
      {!questionData && !error && (
        <div className="card p-6 bg-gray-800/50 rounded-lg">
          <h2 className="text-lg font-semibold mb-4 text-white">Start a New Quiz</h2>
          <div className="form-group mb-4">
            <label htmlFor="process-selector-id" className="block mb-2 text-white">Process Selector ID:</label>
            <input 
              type="text"
              id="process-selector-id"
              value={processId || ''}
              readOnly
              className="w-full p-2 border rounded bg-gray-200"
            />
          </div>
          <div className="form-group mb-4">
            <label htmlFor="center-code" className="block mb-2 text-white">Center Code:</label>
            <input 
              type="text"
              id="center-code"
              value={centerCode}
              readOnly
              className="w-full p-2 border rounded bg-gray-200"
            />
          </div>
          <button 
            onClick={startQuiz}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Start Quiz
          </button>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-center text-red-400 p-4">
          <p>{error}</p>
        </div>
      )}

      {/* Quiz Section */}
      {questionData && (
        <div className="text-white">
          <div className="status-bar p-4 bg-gray-800/50 rounded-lg mb-4">
            <div><strong>Quiz ID:</strong> {quizId}</div>
            <div><strong>Status:</strong> {quizStatus}</div>
          </div>
          
          {/* Timer Display */}
          {currentOptionTimer !== null && (
            <div className="timer-display p-4 bg-blue-900/50 rounded-lg mb-4 text-center">
              <div className="text-2xl font-bold text-blue-300">
                Option {currentOption}: {currentOptionTimer}s
              </div>
              <div className="text-sm text-blue-200 mt-2">
                Capturing frames for option {currentOption}...
              </div>
            </div>
          )}
          
          <video 
            ref={videoRef} 
            autoPlay 
            playsInline 
            muted 
            className="w-full max-w-xl rounded-lg border border-gray-700 mb-4"
          />
          <canvas ref={canvasRef} className="hidden" />
          
          <h2 className="text-xl font-semibold mb-2">{questionData.sub_topic_name}</h2>
          <p className="text-lg mb-4">
            {questionData.question_number}. {questionData.question}
          </p>
          
          <div className="grid gap-2 mb-4">
            {questionData.options.map((opt, idx) => (
              <div key={idx} className="p-2 bg-black/30 rounded-lg">
                {opt}
              </div>
            ))}
          </div>
          
          <button 
            onClick={handleNextQuestion}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-500"
            disabled={quizStatus.includes('Capturing') || quizStatus.includes('Processing')}
          >
            Start Capture & Go to Next Question
          </button>
        </div>
      )}

      {/* Live Feed Logs */}
      {liveFeedLogs.length > 0 && (
        <div className="card p-6 bg-gray-800/50 rounded-lg mt-4">
          <h3 className="text-lg font-semibold mb-2 text-white">Live Hand Raise Feed ({liveFeedLogs.length} events)</h3>
          <div className="max-h-72 overflow-y-auto bg-gray-900 p-2 rounded-lg text-gray-200">
            {liveFeedLogs.map((log, idx) => (
              <p key={idx} className="mb-2 text-sm">
                <strong>[{new Date(log.detection_timestamp).toLocaleTimeString()}]</strong> {log.student_name} raised hand for <strong>{log.option.toUpperCase()}</strong> ({log.is_correct ? '✅ Correct' : '❌ Incorrect'})
              </p>
            ))}
          </div>
        </div>
      )}

      {/* Debug Logs */}
      {debugLogs.length > 0 && (
        <div className="card p-6 bg-gray-800/50 rounded-lg mt-4">
          <h3 className="text-lg font-semibold mb-2 text-white">
            Debug Logs 
            <button 
              onClick={clearDebugLogs}
              className="ml-4 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              Clear
            </button>
          </h3>
          <div className="max-h-72 overflow-y-auto bg-gray-900 p-2 rounded-lg text-gray-200 text-xs font-mono">
            {debugLogs.map((log, idx) => (
              <div key={idx} className="mb-1">{log}</div>
            ))}
          </div>
        </div>
      )}

      {/* Summary Section */}
      {summary && (
        <div className="card p-6 bg-gray-800/50 rounded-lg mt-4">
          <h3 className="text-lg font-semibold mb-2 text-white">Summary of Last Question</h3>
          <pre className="bg-gray-900 p-2 rounded-lg text-gray-200">
            {JSON.stringify(summary, null, 2)}
          </pre>
        </div>
      )}

      {/* Final Results Section */}
      {finalResults && (
        <div className="card p-6 bg-gray-800/50 rounded-lg mt-4">
          <h3 className="text-lg font-semibold mb-2 text-white">Quiz Finished! Final Results:</h3>
          <pre className="bg-gray-900 p-2 rounded-lg text-gray-200">
            {JSON.stringify(finalResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ContentQuiz;