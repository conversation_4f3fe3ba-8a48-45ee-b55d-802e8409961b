import React, { useEffect } from 'react';
import {
  setAccuracyBySubject,
  setAiTutorFeedback,
  setAllQuestionsLog,
  setAttemptBreakdown,
  setConfusedAttempts,
  setErrorTopics,
  setLearningEfficiency,
  setQualityOfTime,
  setQuestionAttemptStatusTimeline,
  setScoreOverview,
  setSubjectSwitchTimeline,
  setSubjectWiseScore,
  setTimeAccuracyTrends,
  setTimeJourney,
  setTimeSpentDistribution,
  useLazyGetAccuracyBySubjectServiceQuery,
  useLazyGetAiTutorFeedbackServiceQuery,
  useLazyGetAllQuestionsLogServiceQuery,
  useLazyGetAttemptBreakdownServiceQuery,
  useLazyGetConfusedAttemptsServiceQuery,
  useLazyGetErrorTopicsServiceQuery,
  useLazyGetLearningEfficiencyServiceQuery,
  useLazyGetQualityOfTimeServiceQuery,
  useLazyGetQuestionAttemptStatusTimelineServiceQuery,
  useLazyGetScoreOverviewServiceQuery,
  useLazyGetSubjectSwitchTimelineServiceQuery,
  useLazyGetSubjectWiseScoreServiceQuery,
  useLazyGetTimeAccuracyTrendsServiceQuery,
  useLazyGetTimeJourneyServiceQuery,
  useLazyGetTimeSpentDistributionServiceQuery
} from './dashboard.slice';
import { useDispatch } from 'react-redux';

const NewDashboard = () => {
  const [getScoreOverview] = useLazyGetScoreOverviewServiceQuery();
  const [getSubjectWiseScore] = useLazyGetSubjectWiseScoreServiceQuery();
  const [getAttemptBreakdown] = useLazyGetAttemptBreakdownServiceQuery();
  const [getAccuracyBySubject] = useLazyGetAccuracyBySubjectServiceQuery();
  const [getTimeAccuracyTrend] = useLazyGetTimeAccuracyTrendsServiceQuery();
  const [getTimeSpentDistribution] = useLazyGetTimeSpentDistributionServiceQuery();
  const [getTimeJourney] = useLazyGetTimeJourneyServiceQuery();
  const [getQualityOfTime] = useLazyGetQualityOfTimeServiceQuery();
  const [getLearningEfficiency] = useLazyGetLearningEfficiencyServiceQuery();
  const [getConfusedAttempts] = useLazyGetConfusedAttemptsServiceQuery();
  const [getSubjectSwitchTimeline] = useLazyGetSubjectSwitchTimelineServiceQuery();
  const [getQuestionAttemptStatusTimeline] = useLazyGetQuestionAttemptStatusTimelineServiceQuery();
  const [getErrorTopics] = useLazyGetErrorTopicsServiceQuery();
  const [getAiTutorFeedback] = useLazyGetAiTutorFeedbackServiceQuery();
  const [getAllQuestionsLog] = useLazyGetAllQuestionsLogServiceQuery();

  const dispatch = useDispatch();

  useEffect(() => {
    handleFetchScoreOverview();
    handleFetchSubjectWiseScore();
    handleFetchAttemptBreakdown();
    handleFetchAccuracyBySubject();
    handleFetchTimeAccuracyTrend();
    handleFetchTimeSpentDistribution();
    handleFetchTimeJourney();
    handleFetchQualityOfTime();
    handleFetchLearningEfficiency();
    handleFetchConfusedAttempts();
    handleFetchSubjectSwitchTimeline();
    handleFetchQuestionAttemptStatusTimeline();
    handleFetchErrorTopics();
    handleFetchAiTutorFeedback();
    handleFetchAllQuestionsLog();
  }, []);

  const handleFetchScoreOverview = async () => {
    try {
      const res = await getScoreOverview({ userId: sessionStorage.getItem('userId') }).unwrap();

      dispatch(setScoreOverview(res));
    } catch (error) {
      console.error('Error fetching score overview:', error);
    }
  };

  const handleFetchSubjectWiseScore = async () => {
    try {
      const res = await getSubjectWiseScore({ userId: sessionStorage.getItem('userId') }).unwrap();

      dispatch(setSubjectWiseScore(res));
    } catch (error) {
      console.error('Error fetching subject-wise score:', error);
    }
  };

  const handleFetchAttemptBreakdown = async () => {
    try {
      const res = await getAttemptBreakdown({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAttemptBreakdown(res));
    } catch (error) {
      console.error('Error fetching attempt breakdown:', error);
    }
  };

  const handleFetchAccuracyBySubject = async () => {
    try {
      const res = await getAccuracyBySubject({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAccuracyBySubject(res));
    } catch (error) {
      console.error('Error fetching accuracy by subject:', error);
    }
  };

  const handleFetchTimeAccuracyTrend = async () => {
    try {
      const res = await getTimeAccuracyTrend({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setTimeAccuracyTrends(res));
    } catch (error) {
      console.error('Error fetching time accuracy trend:', error);
    }
  };

  const handleFetchTimeSpentDistribution = async () => {
    try {
      const res = await getTimeSpentDistribution({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setTimeSpentDistribution(res));
    } catch (error) {
      console.error('Error fetching time spent distribution:', error);
    }
  };

  const handleFetchTimeJourney = async () => {
    try {
      const res = await getTimeJourney({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setTimeJourney(res));
    } catch (error) {
      console.error('Error fetching time journey:', error);
    }
  };

  const handleFetchQualityOfTime = async () => {
    try {
      const res = await getQualityOfTime({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setQualityOfTime(res));
    } catch (error) {
      console.error('Error fetching quality of time:', error);
    }
  };

  const handleFetchLearningEfficiency = async () => {
    try {
      const res = await getLearningEfficiency({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setLearningEfficiency(res));
    } catch (error) {
      console.error('Error fetching learning efficiency:', error);
    }
  };

  const handleFetchConfusedAttempts = async () => {
    try {
      const res = await getConfusedAttempts({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setConfusedAttempts(res));
    } catch (error) {
      console.error('Error fetching confused attempts:', error);
    }
  };

  const handleFetchSubjectSwitchTimeline = async () => {
    try {
      const res = await getSubjectSwitchTimeline({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setSubjectSwitchTimeline(res));
    } catch (error) {
      console.error('Error fetching subject switch timeline:', error);
    }
  };

  const handleFetchQuestionAttemptStatusTimeline = async () => {
    try {
      const res = await getQuestionAttemptStatusTimeline({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setQuestionAttemptStatusTimeline(res));
    } catch (error) {
      console.error('Error fetching question attempt status timeline:', error);
    }
  };

  const handleFetchErrorTopics = async () => {
    try {
      const res = await getErrorTopics({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setErrorTopics(res));
    } catch (error) {
      console.error('Error fetching error topics:', error);
    }
  };

  const handleFetchAiTutorFeedback = async () => {
    try {
      const res = await getAiTutorFeedback({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAiTutorFeedback(res));
    } catch (error) {
      console.error('Error fetching AI tutor feedback:', error);
    }
  };

  const handleFetchAllQuestionsLog = async () => {
    try {
      const res = await getAllQuestionsLog({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAllQuestionsLog(res));
    } catch (error) {
      console.error('Error fetching all questions log:', error);
    }
  };

  return <div>NewDashboard</div>;
};

export default NewDashboard;
