import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import {
  authApi,
  directorApi,
  menuApi,
  processSelectorApi,
  chatApi,
  centerCounselorPanelApi,
  createOwnTestApi,
  teacherDashboardApi,
  eventDetailsApi,
  liveStreamingApi,
  CenterTraineeDashboardApi,
  createOwnTestBioApi,
  faceRegisterApi,
  createOwnTestMathApi,
  createOwnTestPhysicsApi,
  createOwnTestChemistryApi,
  CenterliveViewerApi,
  parentApi,
  CreatePhysicsTestApi,
  onBroadingAssessmentApi,
  PaperBasedMathsTestApi,
  PaperBasedBioTestApi,
  PaperBasedPhysicsTestApi,
  PaperBasedChemistryTestApi,
  DashboardUserDetailsApi,
  VirtualLabsApi,
  UpcomingEventsApi,
  studentDashboardApi
} from '../api/api';
import authReducer from '../../pages/auth/auth.slice';
import menuReducer from '../../components/Navbar/navbar.slice';
import processSelectorReducer from '../../pages/screens/directorPanel/processSelector/processSelector.slice';
import liveStreamingReducer from '../../pages/screens/teacherPanel/teacherLiveStreaming/teacherLiveStreaming.slice';
import onBroadingAssessmentReducer from '../../pages/screens/studentPanel/onBroadingAssesment/onBroadingAssessment.slice';
import eventReducer from '../../pages/screens/directorPanel/events/events.Slice';
import learnpractivalReducer from '../../pages/screens/studentPanel/learnPractically/learnPractically.slice';
import studentDashboardReducer from '../../pages/screens/studentPanel/Dashboard/dashboard.slice';
const rootReducer = combineReducers({
  [authApi.reducerPath]: authApi.reducer,
  [menuApi.reducerPath]: menuApi.reducer,
  [directorApi.reducerPath]: directorApi.reducer,

  [processSelectorApi.reducerPath]: processSelectorApi.reducer,
  [centerCounselorPanelApi.reducerPath]: centerCounselorPanelApi.reducer,
  [chatApi.reducerPath]: chatApi.reducer,
  [createOwnTestApi.reducerPath]: createOwnTestApi.reducer,
  [teacherDashboardApi.reducerPath]: teacherDashboardApi.reducer,
  [eventDetailsApi.reducerPath]: eventDetailsApi.reducer,
  [liveStreamingApi.reducerPath]: liveStreamingApi.reducer,
  [CenterTraineeDashboardApi.reducerPath]: CenterTraineeDashboardApi.reducer,
  [createOwnTestBioApi.reducerPath]: createOwnTestBioApi.reducer,
  [faceRegisterApi.reducerPath]: faceRegisterApi.reducer,
  [createOwnTestMathApi.reducerPath]: createOwnTestMathApi.reducer,
  [createOwnTestPhysicsApi.reducerPath]: createOwnTestPhysicsApi.reducer,
  [createOwnTestChemistryApi.reducerPath]: createOwnTestChemistryApi.reducer,
  [CenterliveViewerApi.reducerPath]: CenterliveViewerApi.reducer,
  [parentApi.reducerPath]: parentApi.reducer,
  [onBroadingAssessmentApi.reducerPath]: onBroadingAssessmentApi.reducer,
  [CreatePhysicsTestApi.reducerPath]: CreatePhysicsTestApi.reducer,
  [PaperBasedMathsTestApi.reducerPath]: PaperBasedMathsTestApi.reducer,
  [PaperBasedBioTestApi.reducerPath]: PaperBasedBioTestApi.reducer,
  [PaperBasedChemistryTestApi.reducerPath]: PaperBasedChemistryTestApi.reducer,
  [PaperBasedPhysicsTestApi.reducerPath]: PaperBasedPhysicsTestApi.reducer,
  [DashboardUserDetailsApi.reducerPath]: DashboardUserDetailsApi.reducer,
  [VirtualLabsApi.reducerPath]: VirtualLabsApi.reducer,
  [UpcomingEventsApi.reducerPath]: UpcomingEventsApi.reducer,
  [studentDashboardApi.reducerPath]: studentDashboardApi.reducer,
  auth: authReducer,
  menu: menuReducer,
  processSelector: processSelectorReducer,
  liveStreaming: liveStreamingReducer,
  onBroadingAssessment: onBroadingAssessmentReducer,
  events: eventReducer,
  learnPractically: learnpractivalReducer,
  studentDashboard: studentDashboardReducer
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      directorApi.middleware,
      menuApi.middleware,
      chatApi.middleware,
      processSelectorApi.middleware,
      centerCounselorPanelApi.middleware,
      createOwnTestApi.middleware,
      teacherDashboardApi.middleware,
      eventDetailsApi.middleware,
      liveStreamingApi.middleware,
      CenterTraineeDashboardApi.middleware,
      createOwnTestBioApi.middleware,
      createOwnTestMathApi.middleware,
      createOwnTestPhysicsApi.middleware,
      createOwnTestChemistryApi.middleware,
      faceRegisterApi.middleware,
      CenterliveViewerApi.middleware,
      parentApi.middleware,
      onBroadingAssessmentApi.middleware,
      CreatePhysicsTestApi.middleware,
      PaperBasedMathsTestApi.middleware,
      PaperBasedBioTestApi.middleware,
      PaperBasedPhysicsTestApi.middleware,
      PaperBasedChemistryTestApi.middleware,
      DashboardUserDetailsApi.middleware,
      VirtualLabsApi.middleware,
      UpcomingEventsApi.middleware,
      studentDashboardApi.middleware
    ),

  devTools: import.meta.env.VITE_ENV
});

setupListeners(store.dispatch);
