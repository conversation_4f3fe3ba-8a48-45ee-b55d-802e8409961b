// import React, { useState } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import {
//   faChalkboardTeacher,
//   faUser,
//   faEnvelope,
//   faPhone,
//   faCalendar,
//   faSpinner,
//   faArrowRight,
//   faArrowLeft,
//   faCheck,
//   faPen
// } from '@fortawesome/free-solid-svg-icons';
// import Input from '../../../../components/Field/Input';
// import Button from '../../../../components/Field/Button';
// import { useAddFacultyMutation } from './addFaculty.slice';
// import Toastify from '../../../../components/PopUp/Toastify';

// const AddFaculty = () => {
//   const [facultyForm, setFacultyForm] = useState({
//     first_name: '',
//     last_name: '',
//     email: '',
//     phone: '',
//     dob: ''
//   });
//   const [res, setRes] = useState(null);
//   const [step, setStep] = useState(0);
//   const [direction, setDirection] = useState(1);
//   const [error, setError] = useState('');
//   const [showPreview, setShowPreview] = useState(false);
//   const [editField, setEditField] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [addFacultyService] = useAddFacultyMutation();

//   const fields = [
//     {
//       name: 'first_name',
//       placeholder: 'First Name',
//       type: 'text',
//       required: true,
//       leftIcon: faUser,
//       animationIcon: (
//         <motion.div
//           animate={{ rotate: [0, 10, -10, 0] }}
//           transition={{ repeat: Infinity, duration: 2 }}
//           className="text-[var(--color-counselor)]"
//         >
//           <FontAwesomeIcon icon={faUser} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'last_name',
//       placeholder: 'Last Name',
//       type: 'text',
//       required: true,
//       leftIcon: faUser,
//       animationIcon: (
//         <motion.div
//           animate={{ scale: [1, 1.1, 1] }}
//           transition={{ repeat: Infinity, duration: 1.5 }}
//           className="text-[var(--color-counselor)]"
//         >
//           <FontAwesomeIcon icon={faUser} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'email',
//       placeholder: 'Email Address',
//       type: 'email',
//       required: true,
//       leftIcon: faEnvelope,
//       animationIcon: (
//         <motion.div
//           animate={{ y: [0, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 1.8 }}
//           className="text-[var(--color-counselor)]"
//         >
//           <FontAwesomeIcon icon={faEnvelope} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'phone',
//       placeholder: 'Phone (+91xxxxxxxxxx)',
//       type: 'tel',
//       required: true,
//       leftIcon: faPhone,
//       animationIcon: (
//         <motion.div
//           animate={{ x: [0, 5, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 2 }}
//           className="text-[var(--color-counselor)]"
//         >
//           <FontAwesomeIcon icon={faPhone} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'dob',
//       placeholder: 'Date of Birth',
//       type: 'date',
//       required: true,
//       leftIcon: faCalendar,
//       animationIcon: (
//         <motion.div
//           animate={{ rotateY: [0, 180, 0] }}
//           transition={{ repeat: Infinity, duration: 3 }}
//           className="text-[var(--color-counselor)]"
//         >
//           <FontAwesomeIcon icon={faCalendar} size="2x" />
//         </motion.div>
//       )
//     }
//   ];

//   const totalSteps = fields.length;
//   const currentField = fields[step];
//   const currentStep = step + 1;

//   const validateField = (field, value) => {
//     if (!value) return setError('This field is required'), false;
//     if (field.name === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))
//       return setError('Invalid email format'), false;
//     if (field.name === 'phone' && !/^\+91\d{10}$/.test(value))
//       return setError('Phone must be in +91XXXXXXXXXX format'), false;
//     setError('');
//     return true;
//   };

//   const handleNext = () => {
//     if (!validateField(currentField, facultyForm[currentField.name])) return;
//     if (step === totalSteps - 1) setShowPreview(true);
//     else {
//       setDirection(1);
//       setStep(step + 1);
//     }
//   };

//   const handleBack = () => {
//     if (showPreview) setShowPreview(false);
//     else {
//       setDirection(-1);
//       setStep(step - 1);
//     }
//   };

//   const handleSaveEdit = () => {
//     const field = fields.find((f) => f.name === editField);
//     if (!validateField(field, facultyForm[field.name])) return;
//     setEditField(null);
//   };

//   const handleSubmit = async () => {
//     const invalid = fields.find((f) => !validateField(f, facultyForm[f.name]));
//     if (invalid) return;
//     setLoading(true);

//     try {
//       await addFacultyService(facultyForm).unwrap();
//       setRes({ status: 'success', message: 'Faculty added successfully!' });

//       setTimeout(() => {
//         window.location.href = '/sasthra';
//       }, 1500);
//     } catch (err) {
//       console.error('Add faculty failed:', err);
//       setRes(err);
//       setLoading(false);
//       setFacultyForm({
//         first_name: '',
//         last_name: '',
//         email: '',
//         phone: '',
//         dob: ''
//       });
//     }
//   };

//   const cardVariants = {
//     enter: (dir) => ({ x: dir > 0 ? 300 : -300, opacity: 0, scale: 0.8 }),
//     center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.4 } },
//     exit: (dir) => ({
//       x: dir > 0 ? -300 : 300,
//       opacity: 0,
//       scale: 0.8,
//       transition: { duration: 0.4 }
//     })
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-counselor)]/5 to-white/50 relative px-4 py-12 overflow-hidden">
//       {/* Animated background elements */}
//       <motion.div
//         animate={{
//           x: [0, 100, 0],
//           y: [0, -50, 0],
//           rotate: [0, 5, 0]
//         }}
//         transition={{
//           duration: 15,
//           repeat: Infinity,
//           ease: "easeInOut"
//         }}
//         className="absolute top-20 right-20 w-64 h-64 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"
//       ></motion.div>

//       <motion.div
//         animate={{
//           scale: [1, 1.05, 1],
//           opacity: [0.8, 1, 0.8]
//         }}
//         transition={{
//           duration: 8,
//           repeat: Infinity,
//           ease: "easeInOut"
//         }}
//         className="absolute bottom-20 left-20 w-72 h-72 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"
//       ></motion.div>

//       {loading && (
//         <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
//           <motion.div
//             initial={{ scale: 0.9, opacity: 0 }}
//             animate={{ scale: 1, opacity: 1 }}
//             className="text-center p-8 bg-white rounded-2xl shadow-xl border border-[var(--color-counselor)]/20">

//             <motion.div
//               animate={{ rotate: 360 }}
//               transition={{ repeat: Infinity, duration: 1.5, ease: "linear" }}
//               className="mb-4"
//             >
//               <FontAwesomeIcon
//                 icon={faSpinner}
//                 className="text-[var(--color-counselor)] text-5xl"
//               />
//             </motion.div>

//             <h3 className="text-xl font-medium text-gray-800">Creating Faculty Profile</h3>
//             <p className="text-gray-600 mt-2">Please wait while we save the details...</p>
//             <motion.div
//               className="mt-4 h-1 bg-gray-200 rounded-full overflow-hidden"
//               initial={{ width: 0 }}
//               animate={{ width: '100%' }}
//               transition={{ duration: 1.5 }}>
//               <div className="h-full bg-[var(--color-counselor)]"></div>
//             </motion.div>
//           </motion.div>
//         </div>
//       )}

//       <Toastify res={res || error} resClear={() => setRes(null)} />

//       <motion.div
//         className="w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden relative z-10"
//         initial={{ opacity: 0, y: 50 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.5 }}
//         whileHover={{ scale: 1.005 }}
//       >
//         {/* Header with gradient */}
//         <motion.div
//           className="bg-gradient-to-r from-[var(--color-counselor)] to-[var(--color-counselor)]/90 p-6 text-white"
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           transition={{ delay: 0.2 }}
//         >
//           <div className="flex items-center justify-between">
//             <div className="flex items-center">
//               <motion.div
//                 className="bg-white/20 p-3 rounded-full mr-4"
//                 whileHover={{ scale: 1.1 }}
//               >
//                 <FontAwesomeIcon icon={faChalkboardTeacher} className="text-xl" />
//               </motion.div>
//               <div>
//                 <h2 className="text-2xl font-bold">Add New Faculty</h2>
//                 <p className="text-white/80 text-sm">Complete all fields to register</p>
//               </div>
//             </div>
//             <motion.div
//               className="bg-white/20 px-3 py-1 rounded-full text-sm"
//               whileHover={{ scale: 1.05 }}
//             >
//               Step {showPreview ? totalSteps : currentStep} of {totalSteps}
//             </motion.div>
//           </div>

//           {/* Progress bar */}
//           <div className="mt-4 relative h-2 bg-white/30 rounded-full">
//             <motion.div
//               className="absolute top-0 left-0 h-full bg-white rounded-full"
//               animate={{
//                 width: `${((showPreview ? totalSteps : currentStep) / totalSteps) * 100}%`
//               }}
//               transition={{ duration: 0.5 }}
//             />
//           </div>
//         </motion.div>

//         {/* Form content */}
//         <div className="p-8">
//           {showPreview ? (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               className="space-y-6"
//             >
//               <motion.h3
//                 className="text-xl font-semibold text-gray-800 flex items-center"
//                 initial={{ x: -20 }}
//                 animate={{ x: 0 }}
//                 transition={{ type: "spring", stiffness: 300 }}
//               >
//                 <FontAwesomeIcon
//                   icon={faCheck}
//                   className="mr-2 text-[var(--color-counselor)]"
//                 />
//                 Confirm Faculty Details
//               </motion.h3>

//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 {fields.map((f) => (
//                   <motion.div
//                     key={f.name}
//                     className="bg-gray-50 p-4 rounded-lg border border-gray-200"
//                     whileHover={{ y: -5 }}
//                     transition={{ type: "spring", stiffness: 400 }}
//                   >
//                     <div className="flex justify-between items-start">
//                       <div>
//                         <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           {f.placeholder}
//                         </p>
//                         {editField === f.name ? (
//                           <div className="mt-1 flex items-center gap-2">
//                             <Input
//                               name={f.name}
//                               type={f.type}
//                               value={facultyForm[f.name]}
//                               onChange={(e) =>
//                                 setFacultyForm({ ...facultyForm, [f.name]: e.target.value })
//                               }
//                               className="border-b border-gray-400 px-1 py-1 w-full bg-transparent text-gray-800 focus:ring-0 focus:border-[var(--color-counselor)]"
//                               autoFocus
//                             />
//                             <motion.button
//                               onClick={handleSaveEdit}
//                               className="text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
//                               whileHover={{ scale: 1.2 }}
//                               whileTap={{ scale: 0.9 }}
//                             >
//                               <FontAwesomeIcon icon={faCheck} />
//                             </motion.button>
//                           </div>
//                         ) : (
//                           <p className="mt-1 text-gray-800">
//                             {f.name === 'dob'
//                               ? new Date(facultyForm[f.name]).toLocaleDateString()
//                               : facultyForm[f.name]}
//                           </p>
//                         )}
//                       </div>
//                       {editField !== f.name && (
//                         <motion.button
//                           onClick={() => setEditField(f.name)}
//                           className="text-xs text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
//                           whileHover={{ scale: 1.1 }}
//                           whileTap={{ scale: 0.9 }}
//                         >
//                           <FontAwesomeIcon icon={faPen} />
//                         </motion.button>
//                       )}
//                     </div>
//                   </motion.div>
//                 ))}
//               </div>

//               <div className="pt-4 flex justify-between border-t border-gray-200">
//                 <Button
//                   name="Back"
//                   onClick={handleBack}
//                   className="flex items-center gap-2 px-6 py-2 cursor-pointer bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
//                   leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
//                 />
//                 <Button
//                   type="submit"
//                   name="Submit Faculty"
//                   onClick={handleSubmit}
//                   disabled={loading}
//                   className={`px-6 py-2 rounded-lg text-white font-medium transition-all ${
//                     loading
//                       ? 'bg-[var(--color-counselor)]/80 cursor-wait'
//                       : 'bg-[var(--color-counselor)] hover:cursor-pointer hover:bg-[var(--color-counselor)]/90'
//                   }`}
//                   leftIcon={!loading && <FontAwesomeIcon icon={faChalkboardTeacher} />}
//                 />
//               </div>
//             </motion.div>
//           ) : (
//             <div className="min-h-[300px] relative">
//               <AnimatePresence initial={false} custom={direction}>
//                 <motion.div
//                   key={step}
//                   custom={direction}
//                   variants={cardVariants}
//                   initial="enter"
//                   animate="center"
//                   exit="exit"
//                   className="absolute inset-0 p-4">
//                   {error && (
//                     <motion.p
//                       initial={{ opacity: 0, y: -10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       className="text-red-500 text-sm mb-4 text-center bg-red-50 py-2 px-4 rounded-lg">
//                       {error}
//                     </motion.p>
//                   )}

//                   <div className="text-center mb-8">
//                     <motion.div
//                       initial={{ scale: 0 }}
//                       animate={{ scale: 1 }}
//                       transition={{ type: "spring", stiffness: 500 }}
//                     >
//                       {currentField.animationIcon}
//                     </motion.div>
//                     <motion.h3
//                       className="text-xl font-semibold text-gray-800 mt-2"
//                       initial={{ y: 10 }}
//                       animate={{ y: 0 }}
//                       transition={{ delay: 0.1 }}
//                     >
//                       {currentField.placeholder}
//                     </motion.h3>
//                     <motion.p
//                       className="text-gray-500 mt-1"
//                       initial={{ opacity: 0 }}
//                       animate={{ opacity: 1 }}
//                       transition={{ delay: 0.2 }}
//                     >
//                       Please enter the faculty's {currentField.placeholder.toLowerCase()}
//                     </motion.p>
//                   </div>

//                   <Input
//                     name={currentField.name}
//                     type={currentField.type}
//                     value={facultyForm[currentField.name]}
//                     onChange={(e) =>
//                       setFacultyForm({ ...facultyForm, [currentField.name]: e.target.value })
//                     }
//                     placeholder={currentField.placeholder}
//                     required={currentField.required}
//                     leftIcon={
//                       <FontAwesomeIcon
//                         icon={currentField.leftIcon}
//                         className="text-[var(--color-counselor)]"
//                       />
//                     }
//                     className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
//                     autoFocus
//                   />
//                 </motion.div>
//               </AnimatePresence>

//               <div className="absolute bottom-0 left-0 right-0 px-4 pb-4 flex justify-between">
//                 <Button
//                   name="Back"
//                   onClick={handleBack}
//                   disabled={step === 0}
//                   className={`flex items-center gap-2 px-6 py-2 rounded-lg transition-colors ${
//                     step === 0
//                       ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
//                       : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
//                   }`}
//                   leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
//                 />
//                 <motion.div
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                 >
//                   <Button
//                     name={step === totalSteps - 1 ? 'Review' : 'Next'}
//                     onClick={handleNext}
//                     className="flex items-center gap-2 hover:cursor-pointer px-6 py-2 bg-[var(--color-counselor)] hover:bg-[var(--color-counselor)]/90 text-white rounded-lg transition-colors"
//                     rightIcon={<FontAwesomeIcon icon={faArrowRight} />}
//                   />
//                 </motion.div>
//               </div>
//             </div>
//           )}
//         </div>
//       </motion.div>
//     </div>
//   );
// };

// export default AddFaculty;
// import React, { useState } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import {
//   faChalkboardTeacher,
//   faUser,
//   faEnvelope,
//   faPhone,
//   faCalendar,
//   faSpinner,
//   faArrowRight,
//   faArrowLeft,
//   faCheck,
//   faPen,
//   faFileUpload,
//   faIdCard
// } from '@fortawesome/free-solid-svg-icons';
// import Input from '../../../../components/Field/Input';
// import Button from '../../../../components/Field/Button';
// import { useAddFacultyMutation } from './addFaculty.slice';
// import Toastify from '../../../../components/PopUp/Toastify';

// const AddFaculty = () => {
//   const [facultyForm, setFacultyForm] = useState({
//     first_name: '',
//     last_name: '',
//     email: '',
//     phone: '',
//     dob: '',
//     aadhar_number: '',
//     document_data: {
//       aadhar_card: null,
//       pan_card: null,
//       resume: null
//     }
//   });
//   const [res, setRes] = useState(null);
//   const [step, setStep] = useState(0);
//   const [direction, setDirection] = useState(1);
//   const [error, setError] = useState('');
//   const [showPreview, setShowPreview] = useState(false);
//   const [editField, setEditField] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [addFacultyService] = useAddFacultyMutation();

//   const allowedDocuments = {
//     aadhar_card: {
//       name: 'Aadhar Card',
//       extensions: ['.pdf', '.jpg', '.jpeg', '.png'],
//       maxSizeMB: 5
//     },
//     pan_card: {
//       name: 'PAN Card',
//       extensions: ['.pdf', '.jpg', '.jpeg', '.png'],
//       maxSizeMB: 5
//     },
//     resume: {
//       name: 'Resume',
//       extensions: ['.pdf', '.doc', '.docx'],
//       maxSizeMB: 10
//     }
//   };

//   const fields = [
//     {
//       name: 'first_name',
//       placeholder: 'First Name',
//       type: 'text',
//       required: true,
//       leftIcon: faUser,
//       animationIcon: (
//         <motion.div
//           animate={{ rotate: [0, 10, -10, 0] }}
//           transition={{ repeat: Infinity, duration: 2 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faUser} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'last_name',
//       placeholder: 'Last Name',
//       type: 'text',
//       required: true,
//       leftIcon: faUser,
//       animationIcon: (
//         <motion.div
//           animate={{ scale: [1, 1.1, 1] }}
//           transition={{ repeat: Infinity, duration: 1.5 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faUser} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'email',
//       placeholder: 'Email Address',
//       type: 'email',
//       required: true,
//       leftIcon: faEnvelope,
//       animationIcon: (
//         <motion.div
//           animate={{ y: [0, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 1.8 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faEnvelope} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'phone',
//       placeholder: 'Phone (+91xxxxxxxxxx)',
//       type: 'tel',
//       required: true,
//       leftIcon: faPhone,
//       animationIcon: (
//         <motion.div
//           animate={{ x: [0, 5, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 2 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faPhone} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'dob',
//       placeholder: 'Date of Birth',
//       type: 'date',
//       required: true,
//       leftIcon: faCalendar,
//       animationIcon: (
//         <motion.div
//           animate={{ rotateY: [0, 180, 0] }}
//           transition={{ repeat: Infinity, duration: 3 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faCalendar} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'aadhar_number',
//       placeholder: 'Aadhar Number (12 digits)',
//       type: 'text',
//       required: false,
//       leftIcon: faIdCard,
//       animationIcon: (
//         <motion.div
//           animate={{ scale: [1, 1.1, 1] }}
//           transition={{ repeat: Infinity, duration: 1.5 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faIdCard} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'aadhar_card',
//       placeholder: 'Aadhar Card (PDF, JPG, PNG, max 5MB)',
//       type: 'file',
//       required: false,
//       accept: '.pdf,.jpg,.jpeg,.png',
//       leftIcon: faFileUpload,
//       animationIcon: (
//         <motion.div
//           animate={{ y: [0, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 1.8 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faFileUpload} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'pan_card',
//       placeholder: 'PAN Card (PDF, JPG, PNG, max 5MB)',
//       type: 'file',
//       required: false,
//       accept: '.pdf,.jpg,.jpeg,.png',
//       leftIcon: faFileUpload,
//       animationIcon: (
//         <motion.div
//           animate={{ y: [0, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 1.8 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faFileUpload} size="2x" />
//         </motion.div>
//       )
//     },
//     {
//       name: 'resume',
//       placeholder: 'Resume (PDF, DOC, DOCX, max 10MB)',
//       type: 'file',
//       required: false,
//       accept: '.pdf,.doc,.docx',
//       leftIcon: faFileUpload,
//       animationIcon: (
//         <motion.div
//           animate={{ y: [0, -5, 0] }}
//           transition={{ repeat: Infinity, duration: 1.8 }}
//           className="text-[var(--color-counselor)]">
//           <FontAwesomeIcon icon={faFileUpload} size="2x" />
//         </motion.div>
//       )
//     }
//   ];

//   const totalSteps = fields.length;
//   const currentField = fields[step];
//   const currentStep = step + 1;

//   const validateField = (field, value) => {
//     if (field.required && !value) {
//       setError(`${field.placeholder} is required`);
//       return false;
//     }
//     if (field.name === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
//       setError('Invalid email format');
//       return false;
//     }
//     if (field.name === 'phone' && value && !/^\+91\d{10}$/.test(value)) {
//       setError('Phone must be in +91XXXXXXXXXX format');
//       return false;
//     }
//     if (field.name === 'aadhar_number' && value && !/^\d{12}$/.test(value)) {
//       setError('Aadhar number must be 12 digits');
//       return false;
//     }
//     if (field.type === 'file' && value) {
//       const file = value;
//       const docConfig = allowedDocuments[field.name];
//       const fileExt = file.name.split('.').pop().toLowerCase();
//       if (!docConfig.extensions.includes(`.${fileExt}`)) {
//         setError(
//           `${docConfig.name}: Invalid file type. Allowed: ${docConfig.extensions.join(', ')}`
//         );
//         return false;
//       }
//       const fileSizeMB = file.size / (1024 * 1024);
//       if (fileSizeMB > docConfig.maxSizeMB) {
//         setError(`${docConfig.name}: File too large. Max size: ${docConfig.maxSizeMB}MB`);
//         return false;
//       }
//     }
//     setError('');
//     return true;
//   };

//   const handleFileChange = (e, fieldName) => {
//     const file = e.target.files[0];
//     if (
//       file &&
//       validateField(
//         fields.find((f) => f.name === fieldName),
//         file
//       )
//     ) {
//       setFacultyForm({
//         ...facultyForm,
//         document_data: {
//           ...facultyForm.document_data,
//           [fieldName]: file
//         }
//       });
//     } else {
//       setFacultyForm({
//         ...facultyForm,
//         document_data: {
//           ...facultyForm.document_data,
//           [fieldName]: null
//         }
//       });
//     }
//   };

//   const handleNext = () => {
//     const value =
//       currentField.type === 'file'
//         ? facultyForm.document_data[currentField.name]
//         : facultyForm[currentField.name];
//     if (!validateField(currentField, value)) return;
//     if (step === totalSteps - 1) setShowPreview(true);
//     else {
//       setDirection(1);
//       setStep(step + 1);
//     }
//   };

//   const handleBack = () => {
//     if (showPreview) setShowPreview(false);
//     else {
//       setDirection(-1);
//       setStep(step - 1);
//     }
//   };

//   const handleSaveEdit = () => {
//     const field = fields.find((f) => f.name === editField);
//     const value =
//       field.type === 'file' ? facultyForm.document_data[field.name] : facultyForm[field.name];
//     if (!validateField(field, value)) return;
//     setEditField(null);
//   };

//   const handleSubmit = async () => {
//     // Validate all required fields
//     const requiredFields = fields.filter((f) => f.required);
//     for (const field of requiredFields) {
//       const value =
//         field.type === 'file' ? facultyForm.document_data[field.name] : facultyForm[field.name];
//       if (!validateField(field, value)) return;
//     }

//     setLoading(true);
//     const formData = new FormData();
//     formData.append('first_name', facultyForm.first_name);
//     formData.append('last_name', facultyForm.last_name);
//     formData.append('email', facultyForm.email);
//     formData.append('phone', facultyForm.phone);
//     formData.append('dob', facultyForm.dob);
//     formData.append('aadhar_number', facultyForm.aadhar_number || '');

//     // Append files
//     Object.keys(facultyForm.document_data).forEach((key) => {
//       if (facultyForm.document_data[key]) {
//         formData.append(key, facultyForm.document_data[key]);
//       }
//     });

//     try {
//       const response = await addFacultyService(formData).unwrap();
//       setRes({ status: 'success', message: response.message || 'Faculty added successfully!' });

//       setTimeout(() => {
//         window.location.href = '/sasthra';
//       }, 1500);
//     } catch (err) {
//       console.error('Add faculty failed:', err);
//       if (err.data?.errors) {
//         setError(err.data.errors.join('; '));
//       } else {
//         setError(err.data?.message || 'Failed to submit faculty request');
//       }
//       setRes({ status: 'error', message: err.data?.message || 'Failed to submit faculty request' });
//       setLoading(false);
//     }
//   };

//   const cardVariants = {
//     enter: (dir) => ({ x: dir > 0 ? 300 : -300, opacity: 0, scale: 0.8 }),
//     center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.4 } },
//     exit: (dir) => ({
//       x: dir > 0 ? -300 : 300,
//       opacity: 0,
//       scale: 0.8,
//       transition: { duration: 0.4 }
//     })
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-counselor)]/5 to-white/50 relative px-4 py-12 overflow-hidden">
//       {/* Animated background elements */}
//       <motion.div
//         animate={{
//           x: [0, 100, 0],
//           y: [0, -50, 0],
//           rotate: [0, 5, 0]
//         }}
//         transition={{
//           duration: 15,
//           repeat: Infinity,
//           ease: 'easeInOut'
//         }}
//         className="absolute top-20 right-20 w-64 h-64 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"></motion.div>

//       <motion.div
//         animate={{
//           scale: [1, 1.05, 1],
//           opacity: [0.8, 1, 0.8]
//         }}
//         transition={{
//           duration: 8,
//           repeat: Infinity,
//           ease: 'easeInOut'
//         }}
//         className="absolute bottom-20 left-20 w-72 h-72 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"></motion.div>

//       {loading && (
//         <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
//           <motion.div
//             initial={{ scale: 0.9, opacity: 0 }}
//             animate={{ scale: 1, opacity: 1 }}
//             className="text-center p-8 bg-white rounded-2xl shadow-xl border border-[var(--color-counselor)]/20">
//             <motion.div
//               animate={{ rotate: 360 }}
//               transition={{ repeat: Infinity, duration: 1.5, ease: 'linear' }}
//               className="mb-4">
//               <FontAwesomeIcon
//                 icon={faSpinner}
//                 className="text-[var(--color-counselor)] text-5xl"
//               />
//             </motion.div>
//             <h3 className="text-xl font-medium text-gray-800">Creating Faculty Profile</h3>
//             <p className="text-gray-600 mt-2">Please wait while we save the details...</p>
//             <motion.div
//               className="mt-4 h-1 bg-gray-200 rounded-full overflow-hidden"
//               initial={{ width: 0 }}
//               animate={{ width: '100%' }}
//               transition={{ duration: 1.5 }}>
//               <div className="h-full bg-[var(--color-counselor)]"></div>
//             </motion.div>
//           </motion.div>
//         </div>
//       )}

//       <Toastify
//         res={res || (error && { status: 'error', message: error })}
//         resClear={() => setRes(null)}
//       />

//       <motion.div
//         className="w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden relative z-10"
//         initial={{ opacity: 0, y: 50 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.5 }}
//         whileHover={{ scale: 1.005 }}>
//         {/* Header with gradient */}
//         <motion.div
//           className="bg-gradient-to-r from-[var(--color-counselor)] to-[var(--color-counselor)]/90 p-6 text-white"
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           transition={{ delay: 0.2 }}>
//           <div className="flex items-center justify-between">
//             <div className="flex items-center">
//               <motion.div className="bg-white/20 p-3 rounded-full mr-4" whileHover={{ scale: 1.1 }}>
//                 <FontAwesomeIcon icon={faChalkboardTeacher} className="text-xl" />
//               </motion.div>
//               <div>
//                 <h2 className="text-2xl font-bold">Add New Faculty</h2>
//                 <p className="text-white/80 text-sm">Complete all fields to register</p>
//               </div>
//             </div>
//             <motion.div
//               className="bg-white/20 px-3 py-1 rounded-full text-sm"
//               whileHover={{ scale: 1.05 }}>
//               Step {showPreview ? totalSteps : currentStep} of {totalSteps}
//             </motion.div>
//           </div>

//           {/* Progress bar */}
//           <div className="mt-4 relative h-2 bg-white/30 rounded-full">
//             <motion.div
//               className="absolute top-0 left-0 h-full bg-white rounded-full"
//               animate={{
//                 width: `${((showPreview ? totalSteps : currentStep) / totalSteps) * 100}%`
//               }}
//               transition={{ duration: 0.5 }}
//             />
//           </div>
//         </motion.div>

//         {/* Form content */}
//         <div className="p-8">
//           {showPreview ? (
//             <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6">
//               <motion.h3
//                 className="text-xl font-semibold text-gray-800 flex items-center"
//                 initial={{ x: -20 }}
//                 animate={{ x: 0 }}
//                 transition={{ type: 'spring', stiffness: 300 }}>
//                 <FontAwesomeIcon icon={faCheck} className="mr-2 text-[var(--color-counselor)]" />
//                 Confirm Faculty Details
//               </motion.h3>

//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 {fields.map((f) => (
//                   <motion.div
//                     key={f.name}
//                     className="bg-gray-50 p-4 rounded-lg border border-gray-200"
//                     whileHover={{ y: -5 }}
//                     transition={{ type: 'spring', stiffness: 400 }}>
//                     <div className="flex justify-between items-start">
//                       <div>
//                         <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           {f.placeholder}
//                         </p>
//                         {editField === f.name && f.type !== 'file' ? (
//                           <div className="mt-1 flex items-center gap-2">
//                             <Input
//                               name={f.name}
//                               type={f.type}
//                               value={facultyForm[f.name]}
//                               onChange={(e) =>
//                                 setFacultyForm({ ...facultyForm, [f.name]: e.target.value })
//                               }
//                               className="border-b border-gray-400 px-1 py-1 w-full bg-transparent text-gray-800 focus:ring-0 focus:border-[var(--color-counselor)]"
//                               autoFocus
//                             />
//                             <motion.button
//                               onClick={handleSaveEdit}
//                               className="text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
//                               whileHover={{ scale: 1.2 }}
//                               whileTap={{ scale: 0.9 }}>
//                               <FontAwesomeIcon icon={faCheck} />
//                             </motion.button>
//                           </div>
//                         ) : (
//                           <p className="mt-1 text-gray-800">
//                             {f.type === 'file'
//                               ? facultyForm.document_data[f.name]
//                                 ? facultyForm.document_data[f.name].name
//                                 : 'Not uploaded'
//                               : f.name === 'dob' && facultyForm[f.name]
//                                 ? new Date(facultyForm[f.name]).toLocaleDateString()
//                                 : facultyForm[f.name] || 'Not provided'}
//                           </p>
//                         )}
//                       </div>
//                       {f.type !== 'file' && editField !== f.name && (
//                         <motion.button
//                           onClick={() => setEditField(f.name)}
//                           className="text-xs text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
//                           whileHover={{ scale: 1.1 }}
//                           whileTap={{ scale: 0.9 }}>
//                           <FontAwesomeIcon icon={faPen} />
//                         </motion.button>
//                       )}
//                     </div>
//                   </motion.div>
//                 ))}
//               </div>

//               <div className="pt-4 flex justify-between border-t border-gray-200">
//                 <Button
//                   name="Back"
//                   onClick={handleBack}
//                   className="flex items-center gap-2 px-6 py-2 cursor-pointer bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
//                   leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
//                 />
//                 <Button
//                   type="submit"
//                   name="Submit Faculty"
//                   onClick={handleSubmit}
//                   disabled={loading}
//                   className={`px-6 py-2 rounded-lg text-white font-medium transition-all ${
//                     loading
//                       ? 'bg-[var(--color-counselor)]/80 cursor-wait'
//                       : 'bg-[var(--color-counselor)] hover:cursor-pointer hover:bg-[var(--color-counselor)]/90'
//                   }`}
//                   leftIcon={!loading && <FontAwesomeIcon icon={faChalkboardTeacher} />}
//                 />
//               </div>
//             </motion.div>
//           ) : (
//             <div className="min-h-[300px] relative">
//               <AnimatePresence initial={false} custom={direction}>
//                 <motion.div
//                   key={step}
//                   custom={direction}
//                   variants={cardVariants}
//                   initial="enter"
//                   animate="center"
//                   exit="exit"
//                   className="absolute inset-0 p-4">
//                   {error && (
//                     <motion.p
//                       initial={{ opacity: 0, y: -10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       className="text-red-500 text-sm mb-4 text-center bg-red-50 py-2 px-4 rounded-lg">
//                       {error}
//                     </motion.p>
//                   )}

//                   <div className="text-center mb-8">
//                     <motion.div
//                       initial={{ scale: 0 }}
//                       animate={{ scale: 1 }}
//                       transition={{ type: 'spring', stiffness: 500 }}>
//                       {currentField.animationIcon}
//                     </motion.div>
//                     <motion.h3
//                       className="text-xl font-semibold text-gray-800 mt-2"
//                       initial={{ y: 10 }}
//                       animate={{ y: 0 }}
//                       transition={{ delay: 0.1 }}>
//                       {currentField.placeholder}
//                     </motion.h3>
//                     <motion.p
//                       className="text-gray-500 mt-1"
//                       initial={{ opacity: 0 }}
//                       animate={{ opacity: 1 }}
//                       transition={{ delay: 0.2 }}>
//                       Please enter the faculty's {currentField.placeholder.toLowerCase()}
//                     </motion.p>
//                   </div>

//                   {currentField.type === 'file' ? (
//                     <div className="relative">
//                       <input
//                         type="file"
//                         name={currentField.name}
//                         accept={currentField.accept}
//                         onChange={(e) => handleFileChange(e, currentField.name)}
//                         className="w-full text-gray-800 bg-transparent border-0 border-b-2 border-gray-300 py-3 px-0 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-lg file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-medium file:bg-[var(--color-counselor)]/10 file:text-[var(--color-counselor)] hover:file:bg-[var(--color-counselor)]/20"
//                       />
//                       {facultyForm.document_data[currentField.name] && (
//                         <p className="mt-2 text-sm text-gray-600">
//                           Selected: {facultyForm.document_data[currentField.name].name}
//                         </p>
//                       )}
//                     </div>
//                   ) : (
//                     <Input
//                       name={currentField.name}
//                       type={currentField.type}
//                       value={facultyForm[currentField.name]}
//                       onChange={(e) =>
//                         setFacultyForm({ ...facultyForm, [currentField.name]: e.target.value })
//                       }
//                       placeholder={currentField.placeholder}
//                       required={currentField.required}
//                       leftIcon={
//                         <FontAwesomeIcon
//                           icon={currentField.leftIcon}
//                           className="text-[var(--color-counselor)]"
//                         />
//                       }
//                       className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
//                       autoFocus
//                     />
//                   )}
//                 </motion.div>
//               </AnimatePresence>

//               <div className="absolute bottom-0 left-0 right-0 px-4 pb-4 flex justify-between">
//                 <Button
//                   name="Back"
//                   onClick={handleBack}
//                   disabled={step === 0}
//                   className={`flex items-center gap-2 px-6 py-2 rounded-lg transition-colors ${
//                     step === 0
//                       ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
//                       : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
//                   }`}
//                   leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
//                 />
//                 <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
//                   <Button
//                     name={step === totalSteps - 1 ? 'Review' : 'Next'}
//                     onClick={handleNext}
//                     className="flex items-center gap-2 hover:cursor-pointer px-6 py-2 bg-[var(--color-counselor)] hover:bg-[var(--color-counselor)]/90 text-white rounded-lg transition-colors"
//                     rightIcon={<FontAwesomeIcon icon={faArrowRight} />}
//                   />
//                 </motion.div>
//               </div>
//             </div>
//           )}
//         </div>
//       </motion.div>
//     </div>
//   );
// };

// export default AddFaculty;

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChalkboardTeacher,
  faUser,
  faEnvelope,
  faPhone,
  faCalendar,
  faSpinner,
  faTimes,
  faArrowLeft,
  faArrowRight,
  faCheck,
  faPen,
  faFileUpload,
  faIdCard,
  faBirthdayCake,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import Input from '../../../../components/Field/Input';
import Button from '../../../../components/Field/Button';
import { useAddFacultyMutation } from './addFaculty.slice';
import Toastify from '../../../../components/PopUp/Toastify';

const AddFaculty = () => {
  const [facultyForm, setFacultyForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '+91',
    dob: '',
    aadhar_number: '',
    document_data: {
      aadhar_card: null,
      pan_card: null,
      resume: null
    }
  });
  const [age, setAge] = useState(null);
  const [res, setRes] = useState(null);
  const [step, setStep] = useState(0);
  const [direction, setDirection] = useState(1);
  const [error, setError] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [editField, setEditField] = useState(null);
  const [loading, setLoading] = useState(false);
  const [addFacultyService] = useAddFacultyMutation();

  // Calculate age when DOB changes
  useEffect(() => {
    if (facultyForm.dob) {
      const birthDate = new Date(facultyForm.dob);
      const today = new Date();
      let calculatedAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        calculatedAge--;
      }

      setAge(calculatedAge);
    } else {
      setAge(null);
    }
  }, [facultyForm.dob]);

  // Phone number handling
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Always maintain +91 prefix
    if (!input.startsWith('+91')) {
      setFacultyForm({ ...facultyForm, phone: '+91' });
      return;
    }

    // Remove all non-digit characters except the +
    const digitsOnly = input.replace(/[^\d+]/g, '');

    // Limit to 13 characters (+91 + 10 digits)
    if (digitsOnly.length > 13) {
      return;
    }

    // Format as +91XXXXXXXXXX
    setFacultyForm({ ...facultyForm, phone: digitsOnly });
  };

  const handlePhoneKeyDown = (e) => {
    if (e.key === 'Backspace' || e.key === 'Delete') {
      if (facultyForm.phone.length <= 3) {
        // +91 is 3 chars
        e.preventDefault();
      }
    }
  };

  // Document configuration
  const allowedDocuments = {
    aadhar_card: {
      name: 'Aadhar Card (Optional)',
      extensions: ['.pdf', '.jpg', '.jpeg', '.png'],
      maxSizeMB: 5,
      icon: faIdCard,
      color: 'bg-[var(--color-counselor)]'
    },
    pan_card: {
      name: 'PAN Card (Optional)',
      extensions: ['.pdf', '.jpg', '.jpeg', '.png'],
      maxSizeMB: 5,
      icon: faIdCard,
      color: 'bg-[var(--color-counselor)]'
    },
    resume: {
      name: 'Resume (Optional)',
      extensions: ['.pdf', '.doc', '.docx'],
      maxSizeMB: 10,
      icon: faFileUpload,
      color: 'bg-[var(--color-counselor)]'
    }
  };

  // Form fields configuration
  const fields = [
    {
      name: 'first_name',
      placeholder: 'First Name',
      type: 'text',
      required: true,
      leftIcon: faUser,
      animationIcon: (
        <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faUser} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'last_name',
      placeholder: 'Last Name',
      type: 'text',
      required: true,
      leftIcon: faUser,
      animationIcon: (
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ repeat: Infinity, duration: 1.5 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faUser} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'email',
      placeholder: 'Email Address',
      type: 'email',
      required: true,
      leftIcon: faEnvelope,
      animationIcon: (
        <motion.div
          animate={{ y: [0, -5, 0] }}
          transition={{ repeat: Infinity, duration: 1.8 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faEnvelope} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'phone',
      placeholder: 'Phone (+91XXXXXXXXXX)',
      type: 'tel',
      required: true,
      leftIcon: faPhone,
      animationIcon: (
        <motion.div
          animate={{ x: [0, 5, -5, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faPhone} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'dob',
      placeholder: 'Date of Birth',
      type: 'date',
      required: true,
      leftIcon: faCalendar,
      animationIcon: (
        <motion.div
          animate={{ rotateY: [0, 180, 0] }}
          transition={{ repeat: Infinity, duration: 3 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faCalendar} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'aadhar_number',
      placeholder: 'Aadhar Number (12 digits)',
      type: 'text',
      required: false,
      leftIcon: faIdCard,
      animationIcon: (
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ repeat: Infinity, duration: 1.5 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faIdCard} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'aadhar_card',
      placeholder: 'Aadhar Card (PDF, JPG, PNG, max 5MB)',
      type: 'file',
      required: false,
      accept: '.pdf,.jpg,.jpeg,.png',
      leftIcon: faFileUpload,
      animationIcon: (
        <motion.div
          animate={{ y: [0, -5, 0] }}
          transition={{ repeat: Infinity, duration: 1.8 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faFileUpload} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'pan_card',
      placeholder: 'PAN Card (PDF, JPG, PNG, max 5MB)',
      type: 'file',
      required: false,
      accept: '.pdf,.jpg,.jpeg,.png',
      leftIcon: faFileUpload,
      animationIcon: (
        <motion.div
          animate={{ y: [0, -5, 0] }}
          transition={{ repeat: Infinity, duration: 1.8 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faFileUpload} size="2x" />
        </motion.div>
      )
    },
    {
      name: 'resume',
      placeholder: 'Resume (PDF, DOC, DOCX, max 10MB)',
      type: 'file',
      required: false,
      accept: '.pdf,.doc,.docx',
      leftIcon: faFileUpload,
      animationIcon: (
        <motion.div
          animate={{ y: [0, -5, 0] }}
          transition={{ repeat: Infinity, duration: 1.8 }}
          className="text-[var(--color-counselor)]">
          <FontAwesomeIcon icon={faFileUpload} size="2x" />
        </motion.div>
      )
    }
  ];

  const totalSteps = fields.length;
  const currentField = fields[step];
  const currentStep = step + 1;

  // Field validation
  const validateField = (field, value) => {
    if (field.required && !value) {
      setError(`${field.placeholder} is required`);
      return false;
    }
    if (field.name === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      setError('Invalid email format');
      return false;
    }
    if (field.name === 'phone' && value && !/^\+91\d{10}$/.test(value)) {
      setError('Phone must be in +91XXXXXXXXXX format (10 digits after +91)');
      return false;
    }
    if (field.name === 'aadhar_number' && value) {
      if (!/^\d{12}$/.test(value)) {
        setError('Aadhar number must be exactly 12 digits');
        return false;
      }
      if (value.length > 12) {
        setError('Aadhar number cannot exceed 12 digits');
        return false;
      }
    }
    if (field.name === 'dob' && age !== null && age < 18) {
      setError('Faculty must be at least 18 years old');
      return false;
    }
    if (field.type === 'file' && value) {
      const file = value;
      const docConfig = allowedDocuments[field.name];
      const fileExt = file.name.split('.').pop().toLowerCase();
      if (!docConfig.extensions.includes(`.${fileExt}`)) {
        setError(
          `${docConfig.name}: Invalid file type. Allowed: ${docConfig.extensions.join(', ')}`
        );
        return false;
      }
      const fileSizeMB = file.size / (1024 * 1024);
      if (fileSizeMB > docConfig.maxSizeMB) {
        setError(`${docConfig.name}: File too large. Max size: ${docConfig.maxSizeMB}MB`);
        return false;
      }
    }
    setError('');
    return true;
  };

  // Handle file changes
  const handleFileChange = (e, fieldName) => {
    const file = e.target.files[0];
    if (
      file &&
      validateField(
        fields.find((f) => f.name === fieldName),
        file
      )
    ) {
      setFacultyForm({
        ...facultyForm,
        document_data: {
          ...facultyForm.document_data,
          [fieldName]: file
        }
      });
    } else {
      setFacultyForm({
        ...facultyForm,
        document_data: {
          ...facultyForm.document_data,
          [fieldName]: null
        }
      });
    }
  };

  // Navigation handlers
  const handleNext = () => {
    const value =
      currentField.type === 'file'
        ? facultyForm.document_data[currentField.name]
        : facultyForm[currentField.name];
    if (!validateField(currentField, value)) return;
    if (step === totalSteps - 1) setShowPreview(true);
    else {
      setDirection(1);
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (showPreview) setShowPreview(false);
    else {
      setDirection(-1);
      setStep(step - 1);
    }
  };

  const handleSaveEdit = () => {
    const field = fields.find((f) => f.name === editField);
    const value =
      field.type === 'file' ? facultyForm.document_data[field.name] : facultyForm[field.name];
    if (!validateField(field, value)) return;
    setEditField(null);
  };

  // Form submission
  const handleSubmit = async () => {
    // Validate all required fields
    const requiredFields = fields.filter((f) => f.required);
    for (const field of requiredFields) {
      const value =
        field.type === 'file' ? facultyForm.document_data[field.name] : facultyForm[field.name];
      if (!validateField(field, value)) return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('first_name', facultyForm.first_name);
    formData.append('last_name', facultyForm.last_name);
    formData.append('email', facultyForm.email);
    formData.append('phone', facultyForm.phone);
    formData.append('dob', facultyForm.dob);
    formData.append('aadhar_number', facultyForm.aadhar_number || '');

    // Append files
    Object.keys(facultyForm.document_data).forEach((key) => {
      if (facultyForm.document_data[key]) {
        formData.append(key, facultyForm.document_data[key]);
      }
    });

    try {
      const response = await addFacultyService(formData).unwrap();
      setRes({ status: 'success', message: response.message || 'Faculty added successfully!' });

      setTimeout(() => {
        window.location.href = '/sasthra';
      }, 1500);
    } catch (err) {
      console.error('Add faculty failed:', err);
      if (err.data?.errors) {
        setError(err.data.errors.join('; '));
      } else {
        setError(err.data?.message || 'Failed to submit faculty request');
      }
      setRes({ status: 'error', message: err.data?.message || 'Failed to submit faculty request' });
      setLoading(false);
    }
  };

  // Animation variants
  const cardVariants = {
    enter: (dir) => ({ x: dir > 0 ? 300 : -300, opacity: 0, scale: 0.8 }),
    center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.4 } },
    exit: (dir) => ({
      x: dir > 0 ? -300 : 300,
      opacity: 0,
      scale: 0.8,
      transition: { duration: 0.4 }
    })
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-counselor)]/5 to-white/50 relative px-4 py-12 overflow-hidden">
      {/* Floating animated elements */}
      <motion.div
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
          rotate: [0, 5, 0]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
        className="absolute top-20 right-20 w-64 h-64 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"
      />

      <motion.div
        animate={{
          scale: [1, 1.05, 1],
          opacity: [0.8, 1, 0.8]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
        className="absolute bottom-20 left-20 w-72 h-72 rounded-full bg-[var(--color-counselor)]/10 blur-3xl"
      />

      {/* Loading overlay */}
      {loading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="text-center p-8 bg-white rounded-2xl shadow-xl border border-[var(--color-counselor)]/20">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ repeat: Infinity, duration: 1.5, ease: 'linear' }}
              className="mb-4">
              <FontAwesomeIcon
                icon={faSpinner}
                className="text-[var(--color-counselor)] text-5xl"
              />
            </motion.div>
            <h3 className="text-xl font-medium text-gray-800">Creating Faculty Profile</h3>
            <p className="text-gray-600 mt-2">Please wait while we save the details...</p>
            <motion.div
              className="mt-4 h-1 bg-gray-200 rounded-full overflow-hidden"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 1.5 }}>
              <div className="h-full bg-[var(--color-counselor)]" />
            </motion.div>
          </motion.div>
        </div>
      )}

      <Toastify
        res={res || (error && { status: 'error', message: error })}
        resClear={() => setRes(null)}
      />

      {/* Main form container */}
      <motion.div
        className="w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden relative z-10"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ scale: 1.005 }}>
        {/* Header section */}
        <motion.div
          className="bg-gradient-to-r from-[var(--color-counselor)] to-[var(--color-counselor)]/90 p-6 text-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <motion.div className="bg-white/20 p-3 rounded-full mr-4" whileHover={{ scale: 1.1 }}>
                <FontAwesomeIcon icon={faChalkboardTeacher} className="text-xl" />
              </motion.div>
              <div>
                <h2 className="text-2xl font-bold">Add New Faculty</h2>
                <p className="text-white/80 text-sm">Complete all fields to register</p>
              </div>
            </div>
            <motion.div
              className="bg-white/20 px-3 py-1 rounded-full text-sm"
              whileHover={{ scale: 1.05 }}>
              Step {showPreview ? totalSteps : currentStep} of {totalSteps}
            </motion.div>
          </div>

          {/* Progress bar */}
          <div className="mt-4 relative h-2 bg-white/30 rounded-full">
            <motion.div
              className="absolute top-0 left-0 h-full bg-white rounded-full"
              animate={{
                width: `${((showPreview ? totalSteps : currentStep) / totalSteps) * 100}%`
              }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </motion.div>

        {/* Form content */}
        <div className="p-8">
          {showPreview ? (
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6">
              <motion.h3
                className="text-xl font-semibold text-gray-800 flex items-center"
                initial={{ x: -20 }}
                animate={{ x: 0 }}
                transition={{ type: 'spring', stiffness: 300 }}>
                <FontAwesomeIcon icon={faCheck} className="mr-2 text-[var(--color-counselor)]" />
                Confirm Faculty Details
              </motion.h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fields.map((f) => (
                  <motion.div
                    key={f.name}
                    className="bg-gray-50 p-4 rounded-lg border border-gray-200"
                    whileHover={{ y: -5 }}
                    transition={{ type: 'spring', stiffness: 400 }}>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {f.placeholder}
                        </p>
                        {editField === f.name && f.type !== 'file' ? (
                          <div className="mt-1 flex items-center gap-2">
                            <Input
                              name={f.name}
                              type={f.type}
                              value={facultyForm[f.name]}
                              onChange={(e) =>
                                setFacultyForm({ ...facultyForm, [f.name]: e.target.value })
                              }
                              className="border-b border-gray-400 px-1 py-1 w-full bg-transparent text-gray-800 focus:ring-0 focus:border-[var(--color-counselor)]"
                              autoFocus
                            />
                            <motion.button
                              onClick={handleSaveEdit}
                              className="text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
                              whileHover={{ scale: 1.2 }}
                              whileTap={{ scale: 0.9 }}>
                              <FontAwesomeIcon icon={faCheck} />
                            </motion.button>
                          </div>
                        ) : (
                          <p className="mt-1 text-gray-800">
                            {f.type === 'file'
                              ? facultyForm.document_data[f.name]
                                ? facultyForm.document_data[f.name].name
                                : 'Not uploaded'
                              : f.name === 'dob' && facultyForm[f.name]
                                ? `${new Date(facultyForm[f.name]).toLocaleDateString()} (${age} years)`
                                : facultyForm[f.name] || 'Not provided'}
                          </p>
                        )}
                      </div>
                      {f.type !== 'file' && editField !== f.name && (
                        <motion.button
                          onClick={() => setEditField(f.name)}
                          className="text-xs text-[var(--color-counselor)] hover:text-[var(--color-counselor)]/80"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}>
                          <FontAwesomeIcon icon={faPen} />
                        </motion.button>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="pt-4 flex justify-between border-t border-gray-200">
                <Button
                  name="Back"
                  onClick={handleBack}
                  className="flex items-center gap-2 px-6 py-2 cursor-pointer bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
                  leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
                />
                <Button
                  type="submit"
                  name="Submit Faculty"
                  onClick={handleSubmit}
                  disabled={loading}
                  className={`px-6 py-2 rounded-lg text-white font-medium transition-all ${
                    loading
                      ? 'bg-[var(--color-counselor)]/80 cursor-wait'
                      : 'bg-[var(--color-counselor)] hover:cursor-pointer hover:bg-[var(--color-counselor)]/90'
                  }`}
                  leftIcon={!loading && <FontAwesomeIcon icon={faChalkboardTeacher} />}
                />
              </div>
            </motion.div>
          ) : (
            <div className="min-h-[300px] relative">
              <AnimatePresence initial={false} custom={direction}>
                <motion.div
                  key={step}
                  custom={direction}
                  variants={cardVariants}
                  initial="enter"
                  animate="center"
                  exit="exit"
                  className="absolute inset-0 p-4">
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200 flex items-start">
                      <div className="flex-shrink-0">
                        <FontAwesomeIcon
                          icon={faExclamationTriangle}
                          className="text-red-500 mt-1 mr-2"
                        />
                      </div>
                      <p className="text-red-600 text-sm">{error}</p>
                    </motion.div>
                  )}

                  <div className="text-center mb-8">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'spring', stiffness: 500 }}>
                      {currentField.animationIcon}
                    </motion.div>
                    <motion.h3
                      className="text-xl font-semibold text-gray-800 mt-2"
                      initial={{ y: 10 }}
                      animate={{ y: 0 }}
                      transition={{ delay: 0.1 }}>
                      {currentField.placeholder}
                    </motion.h3>
                  
                  </div>

                  {currentField.type === 'file' ? (
                    <div className="relative">
                      {/* Document Upload Card */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4 }}
                        className={`mb-6 p-5 rounded-2xl ${allowedDocuments[currentField.name].color} shadow-sm hover:shadow-md transition-all duration-300`}
                        whileHover={{
                          y: -5,
                          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                        }}>
                        <div className="flex flex-col items-center text-center">
                          {/* Animated Icon Container */}
                          <motion.div
                            className="relative mb-4"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}>
                            <div
                              className={`absolute inset-0 rounded-full ${allowedDocuments[currentField.name].color.replace('bg-', 'bg-opacity-20 ')} blur-md`}></div>
                            <div
                              className={`p-4 bg-white rounded-xl shadow-md relative z-10 border-2 ${allowedDocuments[currentField.name].color.replace('bg-', 'border-')}`}>
                              <FontAwesomeIcon
                                icon={allowedDocuments[currentField.name].icon}
                                className={`${allowedDocuments[currentField.name].color.replace('bg-', 'text-')} text-3xl`}
                              />
                            </div>
                          </motion.div>

                          {/* Document Info */}
                          <div className="mb-4 ">
                            <h4 className="text-xl font-bold text-gray-800 mb-1">
                              {allowedDocuments[currentField.name].name}
                            </h4>
                          
                          </div>

                          {/* Upload Button */}
                          <label className="cursor-pointer w-full">
                            <motion.div
                              className={`px-6 py-3 bg-white text-[var(--color-counselor)] w-100 mx-auto rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2`}
                              whileHover={{ scale: 1.03 }}
                              whileTap={{ scale: 0.98 }}>
                              <FontAwesomeIcon icon={faFileUpload} />
                              <span>Select File</span>
                            </motion.div>
                            <input
                              type="file"
                              name={currentField.name}
                              accept={currentField.accept}
                              onChange={(e) => handleFileChange(e, currentField.name)}
                              className="hidden"
                            />
                          </label>
                        </div>
                      </motion.div>

                      {/* Uploaded File Preview */}
                      {facultyForm.document_data[currentField.name] && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4 overflow-hidden">
                          <div className="p-4 bg-white rounded-xl border-2 border-green-200 shadow-sm flex items-center">
                            <div
                              className={`p-3 ${allowedDocuments[currentField.name].color.replace('bg-', 'bg-opacity-20 ')} rounded-lg mr-4`}>
                              <FontAwesomeIcon
                                icon={allowedDocuments[currentField.name].icon}
                                className={`${allowedDocuments[currentField.name].color.replace('bg-', 'text-')}`}
                              />
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex justify-between items-center mb-1">
                                <p className="text-sm font-semibold text-gray-800 truncate">
                                  {facultyForm.document_data[currentField.name].name}
                                </p>
                                <span className="text-xs font-medium px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                  {(
                                    facultyForm.document_data[currentField.name].size /
                                    (1024 * 1024)
                                  ).toFixed(2)}{' '}
                                  MB
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div
                                  className="bg-green-500 h-2 rounded-full"
                                  style={{ width: '100%' }}></div>
                              </div>
                            </div>

                            <button
                              onClick={() => {
                                setFacultyForm({
                                  ...facultyForm,
                                  document_data: {
                                    ...facultyForm.document_data,
                                    [currentField.name]: null
                                  }
                                });
                              }}
                              className="ml-4 p-2 text-gray-400 hover:text-red-500 transition-colors"
                              aria-label="Remove file">
                              <FontAwesomeIcon icon={faTimes} />
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  ) : currentField.name === 'dob' ? (
                    <div className="relative">
                      <Input
                        name={currentField.name}
                        type={currentField.type}
                        value={facultyForm[currentField.name]}
                        onChange={(e) =>
                          setFacultyForm({ ...facultyForm, [currentField.name]: e.target.value })
                        }
                        placeholder={currentField.placeholder}
                        required={currentField.required}
                        leftIcon={
                          <FontAwesomeIcon
                            icon={faBirthdayCake}
                            className="text-[var(--color-counselor)]"
                          />
                        }
                        className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
                        autoFocus
                        max={
                          new Date(new Date().setFullYear(new Date().getFullYear() - 18))
                            .toISOString()
                            .split('T')[0]
                        }
                      />
                      {age !== null && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`mt-2 text-sm font-medium flex items-center ${
                            age >= 18 ? 'text-green-600' : 'text-red-600'
                          }`}>
                          <FontAwesomeIcon
                            icon={age >= 18 ? faCheck : faExclamationTriangle}
                            className="mr-1"
                          />
                          Age: {age} years {age >= 18 ? '(Valid)' : '(Must be 18 or older)'}
                        </motion.div>
                      )}
                    </div>
                  ) : currentField.name === 'phone' ? (
                    <div className="relative">
                      <Input
                        name={currentField.name}
                        type={currentField.type}
                        value={facultyForm.phone}
                        onChange={handlePhoneChange}
                        onKeyDown={handlePhoneKeyDown}
                        placeholder={currentField.placeholder}
                        required={currentField.required}
                        leftIcon={
                          <FontAwesomeIcon
                            icon={currentField.leftIcon}
                            className="text-[var(--color-counselor)]"
                          />
                        }
                        className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
                        autoFocus
                        maxLength={13}
                      />
                      {facultyForm.phone.length === 13 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-sm text-green-600 flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-1" />
                          Valid Indian phone number
                        </motion.div>
                      )}
                      {facultyForm.phone.length > 0 && facultyForm.phone.length < 13 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-sm text-red-600 flex items-center">
                          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                          Must be 10 digits after +91
                        </motion.div>
                      )}
                    </div>
                  ) : currentField.name === 'aadhar_number' ? (
                    <div className="relative">
                      <Input
                        name={currentField.name}
                        type={currentField.type}
                        value={facultyForm[currentField.name]}
                        onChange={(e) => {
                          // Only allow numbers and limit to 12 digits
                          const value = e.target.value.replace(/\D/g, '').slice(0, 12);
                          setFacultyForm({ ...facultyForm, [currentField.name]: value });
                        }}
                        placeholder={currentField.placeholder}
                        required={currentField.required}
                        leftIcon={
                          <FontAwesomeIcon
                            icon={currentField.leftIcon}
                            className="text-[var(--color-counselor)]"
                          />
                        }
                        className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
                        autoFocus
                        maxLength={12}
                      />
                      {facultyForm.aadhar_number && facultyForm.aadhar_number.length === 12 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-sm text-green-600 flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-1" />
                          Valid Aadhar number
                        </motion.div>
                      )}
                    </div>
                  ) : (
                    <Input
                      name={currentField.name}
                      type={currentField.type}
                      value={facultyForm[currentField.name]}
                      onChange={(e) =>
                        setFacultyForm({ ...facultyForm, [currentField.name]: e.target.value })
                      }
                      placeholder={currentField.placeholder}
                      required={currentField.required}
                      leftIcon={
                        <FontAwesomeIcon
                          icon={currentField.leftIcon}
                          className="text-[var(--color-counselor)]"
                        />
                      }
                      className="border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0 py-3 focus:ring-0 focus:border-[var(--color-counselor)] transition-all text-gray-800 text-lg"
                      autoFocus
                    />
                  )}
                </motion.div>
              </AnimatePresence>

              {/* Navigation buttons */}
              <div className="absolute top-0 left-0 right-0 px-4 pb-4  pt-4">
                <div className="flex justify-between">
                  <Button
                    name="Back"
                    onClick={handleBack}
                    disabled={step === 0}
                    className={`flex items-center gap-2 px-6 py-2 rounded-lg transition-colors ${
                      step === 0
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                    leftIcon={<FontAwesomeIcon icon={faArrowLeft} />}
                  />
                  <Button
                    name={step === totalSteps - 1 ? 'Review' : 'Next'}
                    onClick={handleNext}
                    disabled={
                      (currentField.name === 'dob' && age !== null && age < 18) ||
                      (currentField.name === 'phone' && !/^\+91\d{10}$/.test(facultyForm.phone))
                    }
                    className={`flex items-center gap-2 px-6 py-2 rounded-lg transition-colors ${
                      (currentField.name === 'dob' && age !== null && age < 18) ||
                      (currentField.name === 'phone' && !/^\+91\d{10}$/.test(facultyForm.phone))
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-[var(--color-counselor)] hover:bg-[var(--color-counselor)]/90 text-white hover:cursor-pointer'
                    }`}
                    rightIcon={<FontAwesomeIcon icon={faArrowRight} />}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default AddFaculty;
