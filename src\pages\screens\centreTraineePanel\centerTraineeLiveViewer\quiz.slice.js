import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Quiz states
  quizData: null,
  isQuizActive: false,
  currentQuestionIndex: 0,
  questionTimer: 10,
  userAnswer: null,
  quizResults: [],
  isQuizLoading: false,
  quizError: null,
  cameraStream: null,
  quizWebSocket: null,
  isHandRaised: false,
  selectedOption: null,
  handDetectionResults: null
};

const quizSlice = createSlice({
  name: 'quiz',
  initialState,
  reducers: {
    setQuizData: (state, action) => {
      state.quizData = action.payload;
    },
    setIsQuizActive: (state, action) => {
      state.isQuizActive = action.payload;
    },
    setCurrentQuestionIndex: (state, action) => {
      state.currentQuestionIndex = action.payload;
    },
    setQuestionTimer: (state, action) => {
      state.questionTimer = action.payload;
    },
    setUserAnswer: (state, action) => {
      state.userAnswer = action.payload;
    },
    setQuizResults: (state, action) => {
      state.quizResults = action.payload;
    },
    addQuizResult: (state, action) => {
      state.quizResults.push(action.payload);
    },
    setIsQuizLoading: (state, action) => {
      state.isQuizLoading = action.payload;
    },
    setQuizError: (state, action) => {
      state.quizError = action.payload;
    },
    setCameraStream: (state, action) => {
      state.cameraStream = action.payload;
    },
    setQuizWebSocket: (state, action) => {
      state.quizWebSocket = action.payload;
    },
    setIsHandRaised: (state, action) => {
      state.isHandRaised = action.payload;
    },
    setSelectedOption: (state, action) => {
      state.selectedOption = action.payload;
    },
    setHandDetectionResults: (state, action) => {
      state.handDetectionResults = action.payload;
    },
    nextQuizQuestion: (state) => {
      if (state.quizData && state.currentQuestionIndex < state.quizData.questions.length - 1) {
        state.currentQuestionIndex += 1;
        state.questionTimer = 10;
        state.userAnswer = null;
        state.selectedOption = null;
        state.isHandRaised = false;
      }
    },
    resetQuiz: (state) => {
      state.quizData = null;
      state.isQuizActive = false;
      state.currentQuestionIndex = 0;
      state.questionTimer = 10;
      state.userAnswer = null;
      state.quizResults = [];
      state.isQuizLoading = false;
      state.quizError = null;
      state.selectedOption = null;
      state.isHandRaised = false;
      state.handDetectionResults = null;
    },
    cleanupQuizCamera: (state) => {
      if (state.cameraStream) {
        // Note: Actual cleanup will be done in the component
        state.cameraStream = null;
      }
      if (state.quizWebSocket) {
        // Note: Actual cleanup will be done in the component
        state.quizWebSocket = null;
      }
    }
  }
});

export const {
  setQuizData,
  setIsQuizActive,
  setCurrentQuestionIndex,
  setQuestionTimer,
  setUserAnswer,
  setQuizResults,
  addQuizResult,
  setIsQuizLoading,
  setQuizError,
  setCameraStream,
  setQuizWebSocket,
  setIsHandRaised,
  setSelectedOption,
  setHandDetectionResults,
  nextQuizQuestion,
  resetQuiz,
  cleanupQuizCamera
} = quizSlice.actions;

export default quizSlice.reducer;
