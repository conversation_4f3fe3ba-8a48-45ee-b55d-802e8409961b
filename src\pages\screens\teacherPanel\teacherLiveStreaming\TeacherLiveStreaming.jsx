﻿import { useState, useEffect, useRef } from "react"
import { Room, RoomEvent, createLocalVideoTrack, createLocalAudioTrack, createLocalScreenTracks } from "livekit-client"
import { useDispatch, useSelector } from "react-redux"
import io from "socket.io-client"
import {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
  useUploadQuizPdfMutation,
  useStartQuizMutation,
  useNextQuestionMutation,
  setStreamingData,
  setIsStreaming,
  setStreamToken,
  setRoomName,
  setError,
  setChatMessages,
  setChatLoading,
  setChatError,
  clearStreamingData,
  setQuizData,
  setQuizObjectId,
  setIsQuizActive,
  setCurrentQuestionIndex,
  setQuestionTimer,
  setQuizResults,
  addQuizResult,
  setIsQuizLoading,
  setQuizError,
  nextQuizQuestion,
  resetQuiz,
} from "./teacherLiveStreaming.slice"

const TeacherLiveStreaming = () => {
  const dispatch = useDispatch()
  const {
    isStreaming,
    streamToken,
    roomName,
    error,
    chatMessages,
    chatLoading,
    chatError,
    // Quiz state
    quizData,
    isQuizActive,
    currentQuestionIndex,
    quizObjectId,
    questionTimer,
    quizResults,
    isQuizLoading,
    quizError
  } = useSelector((state) => state.liveStreaming || {})

  const [startStream] = useStartEnhancedStreamMutation()
  const [stopStream] = useStopEnhancedStreamMutation()
  const [sendChatMessageMutation] = useSendChatMessageMutation()
  const [getChatHistory] = useLazyGetChatHistoryQuery()

  // Quiz mutations
  const [uploadQuizPdf] = useUploadQuizPdfMutation()
  const [startQuizMutation] = useStartQuizMutation()
  const [nextQuestionMutation] = useNextQuestionMutation()

  // LiveKit states
  const [livekitRoom, setLivekitRoom] = useState(null)
  const [livekitConnected, setLivekitConnected] = useState(false)
  const [livekitToken, setLivekitToken] = useState(null)
  const [livekitUrl, setLivekitUrl] = useState(null)
  const [sessionId, setSessionId] = useState(null)

  // Media states
  const [localVideoTrack, setLocalVideoTrack] = useState(null)
  const [localAudioTrack, setLocalAudioTrack] = useState(null)
  const [localScreenTrack, setLocalScreenTrack] = useState(null)
  const [localScreenAudioTrack, setLocalScreenAudioTrack] = useState(null)
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [previewVideoTrack, setPreviewVideoTrack] = useState(null)
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false)
  const [screenStream, setScreenStream] = useState(null)
  const [testScreenShare, setTestScreenShare] = useState(false)

  // UI states
  const [streamStatus, setStreamStatus] = useState("Ready to start streaming")
  const [participants, setParticipants] = useState([])
  const [quality, setQuality] = useState("medium")

  // Chat states
  const [newMessage, setNewMessage] = useState("")
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [unreadMessages, setUnreadMessages] = useState(0)
  const [joinedViewers, setJoinedViewers] = useState([])

  // Socket.IO states
  const [socket, setSocket] = useState(null)
  const [socketConnected, setSocketConnected] = useState(false)

  // Quiz states
  const [selectedPdfFile, setSelectedPdfFile] = useState(null)
  const [isUploadingPdf, setIsUploadingPdf] = useState(false)
  const [showQuizPanel, setShowQuizPanel] = useState(false)
  const [timerInterval, setTimerInterval] = useState(null)

  // Refs
  const videoRef = useRef(null)
  const screenVideoRef = useRef(null)
  const pipCameraRef = useRef(null)
  const livekitRoomRef = useRef(null)

  // Check secure context
  useEffect(() => {
    if (!window.isSecureContext) {
      console.error("❌ App not running in secure context. Camera access requires HTTPS.")
      setStreamStatus("Error: Camera access requires HTTPS.")
      dispatch(setError("Camera access requires a secure context (HTTPS)."))
    }
  }, [])

  // Initialize session ID on mount
  useEffect(() => {
    const userId = sessionStorage.getItem("userId")
    const newSessionId = `teacher_${userId}_${Date.now()}`
    setSessionId(newSessionId)
  }, [])

  // Initialize camera preview when streaming starts
  useEffect(() => {
    if (isStreaming) {
      initializeCameraPreview()
    }
    // Cleanup when isStreaming changes to false
    return () => {
      if (!isStreaming && previewVideoTrack) {
        previewVideoTrack.stop()
        setPreviewVideoTrack(null)
      }
    }
  }, [isStreaming])

  // Attach video track when previewVideoTrack changes
  useEffect(() => {
    if (previewVideoTrack && videoRef.current && !isScreenSharing) {
      previewVideoTrack.attach(videoRef.current)
      console.log("✅ Preview video track attached via useEffect")
    }
  }, [previewVideoTrack, isScreenSharing])

  // Connect to LiveKit room when token and URL are available
  useEffect(() => {
    if (livekitToken && livekitUrl && !livekitRoom) {
      connectToLiveKitRoom()
    }
  }, [livekitToken, livekitUrl])

  // Publish camera track when localVideoTrack is available and room is connected
  useEffect(() => {
    const publishCameraTrack = async () => {
      if (localVideoTrack && livekitRoom && livekitConnected) {
        try {
          // Check if track is already published by looking through published tracks
          const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values())
          const existingPublication = publishedTracks.find(
            (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
          )

          if (!existingPublication) {
            await livekitRoom.localParticipant.publishTrack(localVideoTrack, {
              source: "camera",
              name: "teacher_camera",
            })
            console.log("✅ Camera track published to LiveKit via useEffect with camera source")
          } else {
            console.log("ℹ️ Camera track already published")
          }
        } catch (err) {
          console.error("❌ Failed to publish camera track via useEffect:", err)
        }
      }
    }

    publishCameraTrack()
  }, [localVideoTrack, livekitRoom, livekitConnected])

  // Handle screen stream changes
  useEffect(() => {
    if (screenStream && screenVideoRef.current && isScreenSharing) {
      console.log("🖥️ Setting screen stream to video element")
      screenVideoRef.current.srcObject = screenStream
      // Ensure video plays
      screenVideoRef.current.play().catch((err) => {
        console.warn("⚠️ Screen video autoplay failed:", err)
      })
    }
  }, [screenStream, isScreenSharing])

  // Handle PiP camera attachment during screen sharing
  useEffect(() => {
    if (isScreenSharing && previewVideoTrack && pipCameraRef.current) {
      try {
        // Ensure camera is attached to PiP during screen sharing
        console.log("🎥 Ensuring camera is attached to PiP during screen sharing")
        // Clear any existing srcObject
        if (pipCameraRef.current.srcObject) {
          pipCameraRef.current.srcObject = null
        }
        // Attach the track
        previewVideoTrack.attach(pipCameraRef.current)
        console.log("✅ Camera attached to PiP via useEffect")
        // Ensure video plays
        pipCameraRef.current.play().catch((playErr) => {
          console.warn("⚠️ PiP camera autoplay failed:", playErr)
        })
      } catch (attachErr) {
        console.warn("⚠️ Failed to attach camera to PiP via useEffect:", attachErr)
        // Fallback: try MediaStream approach
        try {
          const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack])
          pipCameraRef.current.srcObject = mediaStream
          console.log("✅ Camera attached to PiP via MediaStream fallback in useEffect")
        } catch (fallbackErr) {
          console.error("❌ PiP camera fallback failed in useEffect:", fallbackErr)
        }
      }
    }
  }, [isScreenSharing, previewVideoTrack])

  // Periodic check to ensure parallel streaming remains active
  useEffect(() => {
    let intervalId
    if (isStreaming && livekitConnected) {
      intervalId = setInterval(() => {
        if (isScreenSharing) {
          // During screen sharing, ensure both camera and screen tracks are published
          ensureParallelStreaming()
        }
      }, 10000) // Check every 10 seconds
      console.log("🔄 Started periodic parallel streaming check")
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
        console.log("🛑 Stopped periodic parallel streaming check")
      }
    }
  }, [isStreaming, livekitConnected, isScreenSharing])

  // HTTP-based chat system (more reliable than Socket.IO)
  useEffect(() => {
    if (isStreaming && sessionId) {
      console.log("💬 TEACHER: Starting HTTP-based chat for session:", sessionId)
      setSocketConnected(true) // Mark as "connected" for UI purposes
      setStreamStatus("Chat system ready")
      // Load initial chat history
      loadChatHistory()
      // Start polling for new messages every 2 seconds
      const pollInterval = setInterval(() => {
        loadChatHistory()
      }, 2000)

      return () => {
        clearInterval(pollInterval)
        setSocketConnected(false)
      }
    }
  }, [isStreaming, sessionId])

  // Load chat history via Redux
  const loadChatHistory = async () => {
    if (!sessionId) return

    try {
      console.log(`🔍 TEACHER: Loading chat history for session: ${sessionId}`)
      dispatch(setChatLoading(true))
      dispatch(setChatError(null))

      const result = await getChatHistory(sessionId)
      if (result.data) {
        const newMessages = result.data
        console.log(`🔍 TEACHER: API returned ${newMessages.length} messages for session ${sessionId}`)

        // Check for new messages to increment unread count
        const currentMessageCount = chatMessages.length
        if (newMessages.length > currentMessageCount) {
          const newCount = newMessages.length - currentMessageCount
          if (newCount > 0 && !isChatOpen) {
            setUnreadMessages((prev) => prev + newCount)
          }
          console.log(`💬 TEACHER: Updated chat with ${newMessages.length} messages`)
        }

        dispatch(setChatMessages(newMessages))
      } else if (result.error) {
        console.log(`❌ TEACHER: Chat history API error:`, result.error)
        dispatch(setChatError(result.error.data || "Failed to load chat history"))
      }
    } catch (error) {
      console.log("❌ TEACHER: Failed to load chat history:", error.message)
      dispatch(setChatError(error.message || "Failed to load chat history"))
    } finally {
      dispatch(setChatLoading(false))
    }
  }

  const connectToLiveKitRoom = async () => {
    try {
      console.log("🔗 Connecting to LiveKit room...")
      const room = new Room()

      // Set up room event listeners
      room.on(RoomEvent.Connected, () => {
        console.log("✅ Connected to LiveKit room")
        setLivekitConnected(true)
        setStreamStatus("Connected to LiveKit room")
        // Log published tracks
        setTimeout(() => {
          const publishedTracks = room.localParticipant.videoTracks
          if (publishedTracks && publishedTracks.values) {
            console.log(
              "📹 Published video tracks:",
              Array.from(publishedTracks.values()).map((pub) => ({
                source: pub.source,
                name: pub.trackName,
                sid: pub.trackSid,
              })),
            )
          } else {
            console.log("📹 Published video tracks: none available yet")
          }
        }, 1000)
      })

      room.on(RoomEvent.Disconnected, () => {
        console.log("❌ Disconnected from LiveKit room")
        setLivekitConnected(false)
        setStreamStatus("Disconnected from LiveKit room")
      })

      room.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log("👤 LiveKit Participant connected:", participant.identity, participant.name)
        setParticipants((prev) => [...prev, participant])
        // Also add to joined viewers for display
        setJoinedViewers((prev) => {
          const existing = prev.find((v) => v.viewer_id === participant.identity)
          if (!existing) {
            return [
              ...prev,
              {
                viewer_id: participant.identity,
                viewer_name: participant.name || participant.identity,
                user_role: "student", // Default role for LiveKit participants
                joined_at: new Date().toISOString(),
                source: "livekit",
              },
            ]
          }
          return prev
        })
      })

      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log("👤 LiveKit Participant disconnected:", participant.identity)
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
        // Also remove from joined viewers
        setJoinedViewers((prev) => prev.filter((v) => v.viewer_id !== participant.identity))
      })

      // Validate LiveKit URL
      if (!livekitUrl) {
        throw new Error("LiveKit URL is not set")
      }

      // Connect to room using dynamic LiveKit URL from backend
      await room.connect(livekitUrl, livekitToken)
      setLivekitRoom(room)
      livekitRoomRef.current = room

      // Publish camera track if available (either localVideoTrack or previewVideoTrack)
      const cameraTrack = localVideoTrack || previewVideoTrack
      if (cameraTrack) {
        try {
          // Publish with camera source metadata
          await room.localParticipant.publishTrack(cameraTrack, {
            source: "camera",
            name: "teacher_camera",
          })
          console.log("✅ Camera track published to LiveKit on connect with camera source")
          // Ensure localVideoTrack is set if we used previewVideoTrack
          if (!localVideoTrack && previewVideoTrack) {
            setLocalVideoTrack(previewVideoTrack)
          }
        } catch (cameraErr) {
          console.error("❌ Failed to publish camera track on connect:", cameraErr)
        }
      } else {
        console.log("⚠️ No camera track available to publish on connect")
      }

      // Create and publish audio track
      try {
        const audioTrack = await createLocalAudioTrack()
        setLocalAudioTrack(audioTrack)
        await room.localParticipant.publishTrack(audioTrack)
        console.log("✅ Audio track published to LiveKit")
      } catch (audioErr) {
        console.warn("⚠️ Could not create audio track:", audioErr)
      }
    } catch (err) {
      console.error("❌ Failed to connect to LiveKit room:", err)
      setStreamStatus("Failed to connect to LiveKit room")
      dispatch(setError(err.message || "Failed to connect to LiveKit room"))
    }
  }

  const initializeCameraPreview = async () => {
    try {
      console.log("🎥 Initializing camera preview...")
      const videoTrack = await createLocalVideoTrack({
        resolution: { width: 640, height: 480 },
        frameRate: 15,
      })

      console.log("✅ Video track created:", videoTrack)
      setPreviewVideoTrack(videoTrack)
      setCameraPermissionGranted(true)

      if (videoRef.current) {
        videoTrack.attach(videoRef.current)
        console.log("✅ Preview video track attached")
      } else {
        console.log("⚠️ videoRef not ready yet, will attach via useEffect")
      }
    } catch (err) {
      console.error("❌ Error initializing camera preview:", err)
      setCameraPermissionGranted(false)
      let errorMessage = "Failed to initialize camera preview"
      if (err.name === "NotAllowedError") {
        errorMessage = "Camera permission denied. Please allow camera access."
      } else if (err.name === "NotFoundError") {
        errorMessage = "No camera found. Please connect a camera."
      } else if (err.name === "NotReadableError") {
        errorMessage = "Camera in use by another application."
      } else if (err.message) {
        errorMessage = err.message
      }
      setStreamStatus(errorMessage)
      dispatch(setError(errorMessage))
    }
  }

  const refreshCameraTrack = async () => {
    try {
      console.log("🔄 Refreshing camera track...")
      // Stop existing track if any
      if (previewVideoTrack) {
        previewVideoTrack.stop()
        previewVideoTrack.detach()
      }

      // Create new track
      const newVideoTrack = await createLocalVideoTrack({
        resolution: { width: 640, height: 480 },
        frameRate: 15,
      })

      console.log("✅ New camera track created:", newVideoTrack)
      setPreviewVideoTrack(newVideoTrack)

      // Attach to appropriate element based on screen sharing state
      if (isScreenSharing && pipCameraRef.current) {
        newVideoTrack.attach(pipCameraRef.current)
        console.log("✅ New camera track attached to PiP")
      } else if (!isScreenSharing && videoRef.current) {
        newVideoTrack.attach(videoRef.current)
        console.log("✅ New camera track attached to main video")
      }

      // Update localVideoTrack if it was using the old previewVideoTrack
      if (localVideoTrack === previewVideoTrack) {
        setLocalVideoTrack(newVideoTrack)
      }

      // Ensure the new track is published to LiveKit if connected
      if (livekitRoom && livekitConnected) {
        try {
          await livekitRoom.localParticipant.publishTrack(newVideoTrack, {
            source: "camera",
            name: "teacher_camera",
          })
          console.log("✅ New camera track published to LiveKit")
        } catch (publishErr) {
          console.warn("⚠️ Failed to publish new camera track:", publishErr)
        }
      }

      return newVideoTrack
    } catch (err) {
      console.error("❌ Failed to refresh camera track:", err)
      throw err
    }
  }

  const ensureParallelStreaming = async () => {
    try {
      console.log("🔄 Ensuring parallel streaming of camera and screen...")
      if (!livekitRoom || !livekitConnected) {
        console.warn("⚠️ LiveKit room not connected")
        return
      }

      // Safely get published tracks with null checks
      const videoTracks = livekitRoom.localParticipant.videoTracks
      if (!videoTracks || !videoTracks.values) {
        console.warn("⚠️ Video tracks not available yet")
        return
      }

      const publishedTracks = Array.from(videoTracks.values())
      console.log(
        "📹 Currently published video tracks:",
        publishedTracks.map((pub) => ({
          source: pub.source,
          name: pub.trackName,
          sid: pub.trackSid,
        })),
      )

      // Ensure camera track is published
      const cameraTrack = localVideoTrack || previewVideoTrack
      const hasCameraTrack = publishedTracks.some(
        (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
      )

      if (cameraTrack && !hasCameraTrack) {
        await livekitRoom.localParticipant.publishTrack(cameraTrack, {
          source: "camera",
          name: "teacher_camera",
        })
        console.log("✅ Camera track ensured for parallel streaming")
      }

      // Ensure screen track is published if screen sharing is active
      if (isScreenSharing && localScreenTrack) {
        const hasScreenTrack = publishedTracks.some(
          (pub) => pub.source === "screen_share" || pub.trackName === "teacher_screen",
        )

        if (!hasScreenTrack) {
          await livekitRoom.localParticipant.publishTrack(localScreenTrack, {
            source: "screen_share",
            name: "teacher_screen",
          })
          console.log("✅ Screen track ensured for parallel streaming")
        }
      }

      console.log("✅ Parallel streaming ensured - both camera and screen tracks active")
    } catch (err) {
      console.error("❌ Failed to ensure parallel streaming:", err)
    }
  }

  // Chat functions
  const sendChatMessage = async () => {
    if (!newMessage.trim() || !socketConnected || !sessionId) return

    const messageData = {
      session_id: sessionId,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem("userId"),
      sender_name: sessionStorage.getItem("name") || "Teacher",
    }

    console.log("📤 TEACHER SENDING MESSAGE WITH SESSION ID:", sessionId)
    console.log("📤 TEACHER MESSAGE DATA:", messageData)

    try {
      dispatch(setChatLoading(true))
      dispatch(setChatError(null))

      const result = await sendChatMessageMutation(messageData)
      if (result.data) {
        console.log("✅ TEACHER: Message sent successfully")
        setNewMessage("")
        // Immediately load chat history to see the new message
        setTimeout(loadChatHistory, 500)
      } else if (result.error) {
        console.error("❌ TEACHER: Failed to send message:", result.error)
        dispatch(setChatError(result.error.data || "Failed to send message"))
      }
    } catch (error) {
      console.error("❌ TEACHER: Error sending message:", error)
      dispatch(setChatError(error.message || "Failed to send message"))
    } finally {
      dispatch(setChatLoading(false))
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendChatMessage()
    }
  }

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen)
    if (!isChatOpen) {
      setUnreadMessages(0)
    }
  }

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getRoleColor = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "text-emerald-600"
      case "student":
        return "text-blue-600"
      case "center_counselor":
        return "text-purple-600"
      default:
        return "text-gray-600"
    }
  }

  const getRoleBadge = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "Teacher"
      case "student":
        return "Student"
      case "center_counselor":
        return "Counselor"
      default:
        return "User"
    }
  }

  // Test Socket.IO connection function
  const testSocketConnection = async () => {
    console.log("🧪 Testing Socket.IO connection...")
    const testUrls = ["https://sasthra.in"]

    for (const url of testUrls) {
      try {
        console.log(`🔍 Testing: ${url}`)
        const testSocket = io(url, {
          transports: ["polling"], // Start with polling only
          timeout: 5000,
          forceNew: true,
        })

        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            testSocket.disconnect()
            reject(new Error("Timeout"))
          }, 5000)

          testSocket.on("connect", () => {
            clearTimeout(timeout)
            console.log(`✅ Test connection successful: ${url}`)
            testSocket.disconnect()
            resolve(url)
          })

          testSocket.on("connect_error", (error) => {
            clearTimeout(timeout)
            console.log(`❌ Test connection failed: ${url} - ${error.message}`)
            testSocket.disconnect()
            reject(error)
          })
        })

        // If we get here, connection was successful
        return url
      } catch (error) {
        console.log(`❌ Test failed for ${url}:`, error.message)
        continue
      }
    }

    throw new Error("All test connections failed")
  }

  // Add a test message function
  const sendTestMessage = () => {
    if (socket && socketConnected) {
      const testMessage = {
        session_id: sessionId,
        message: "Test message from teacher",
        sender_id: sessionStorage.getItem("userId"),
        sender_name: sessionStorage.getItem("name") || "Teacher",
      }

      console.log("🧪 Sending test message:", testMessage)
      socket.emit("chat_message", testMessage)
    } else {
      console.log("❌ Cannot send test message - socket not connected")
    }
  }

  // Health check function
  const checkBackendHealth = async () => {
    const healthUrls = ["https://sasthra.in"]

    console.log("🏥 Checking backend health...")
    for (const url of healthUrls) {
      try {
        console.log(`🔍 Checking: ${url}`)
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })

        if (response.ok) {
          const data = await response.json()
          console.log(`✅ Backend healthy at: ${url}`, data)
          return { url, data }
        }
      } catch (error) {
        console.log(`❌ Health check failed for ${url}:`, error.message)
        continue
      }
    }

    throw new Error("Backend health check failed for all URLs")
  }

  const startStreaming = async () => {
    try {
      // Ensure camera is initialized before starting stream
      if (!previewVideoTrack && !localVideoTrack) {
        console.log("🎥 Camera not initialized, initializing now...")
        await initializeCameraPreview()
        // Wait a bit for the track to be ready
        await new Promise((resolve) => setTimeout(resolve, 500))
      }

      const userId = sessionStorage.getItem("userId")
      console.log("📋 User info:", {
        userId,
        userName: "physics_teacher teacher",
        userRole: "kota_teacher",
        token: "PRESENT",
      })
      console.log("📹 Available tracks before streaming:", {
        previewVideoTrack: !!previewVideoTrack,
        localVideoTrack: !!localVideoTrack,
        cameraPermissionGranted,
      })

      const response = await startStream({
        userId,
        sessionId,
        quality,
        screenShareEnabled: isScreenSharing,
      }).unwrap()

      console.log("Start Enhanced Stream Response:", response)
      console.log("🎫 Stream started:", response)

      // CRITICAL FIX: Use session_id from backend response for chat
      const backendSessionId = response.session_id
      if (backendSessionId && backendSessionId !== sessionId) {
        console.log("🔧 TEACHER: Session ID mismatch detected!")
        console.log(`🔧 TEACHER: Original session ID: ${sessionId}`)
        console.log(`🔧 TEACHER: Backend session ID: ${backendSessionId}`)
        console.log("✅ TEACHER: Using backend session ID for chat")
        // Show alert to user about the fix
        alert(
          `🔧 Teacher Session ID Fixed!\nOriginal: ${sessionId}\nCorrected: ${backendSessionId}\n\nChat will now work with viewers!`,
        )
        setSessionId(backendSessionId) // Update to use backend session ID
      }

      // Store streaming data in Redux
      dispatch(setStreamingData({ streamToken: response.livekit_token, roomName: response.roomName }))
      dispatch(setStreamToken(response.livekit_token))
      dispatch(setRoomName(response.roomName))
      dispatch(setIsStreaming(true))

      // Set LiveKit token and URL from response
      setLivekitToken(response.livekit_token)
      setLivekitUrl(response.livekit_url)

      // Reuse preview track for streaming
      if (previewVideoTrack) {
        console.log("📹 Reusing preview track for streaming")
        setLocalVideoTrack(previewVideoTrack)
      }

      setStreamStatus("LiveKit stream started successfully")
    } catch (err) {
      console.error("❌ Failed to start stream:", err)
      setStreamStatus("Failed to start stream")
      dispatch(setError(err.message || "Failed to start stream"))
    }
  }

  const stopStreaming = async () => {
    try {
      await stopStream({ sessionId }).unwrap()
      cleanupAllResources()
      dispatch(setIsStreaming(false))
      dispatch(clearStreamingData())
      setStreamStatus("Ready to start streaming")
    } catch (err) {
      console.error("❌ Failed to stop stream:", err)
      dispatch(setError(err.message || "Failed to stop stream"))
    }
  }

  const cleanupLiveKitResources = () => {
    if (localVideoTrack) {
      localVideoTrack.stop()
      localVideoTrack.detach()
      setLocalVideoTrack(null)
    }

    if (localAudioTrack) {
      localAudioTrack.stop()
      localAudioTrack.detach()
      setLocalAudioTrack(null)
    }

    if (previewVideoTrack) {
      previewVideoTrack.stop()
      previewVideoTrack.detach()
      setPreviewVideoTrack(null)
    }

    if (localScreenTrack) {
      localScreenTrack.stop()
      localScreenTrack.detach()
      setLocalScreenTrack(null)
    }

    if (localScreenAudioTrack) {
      localScreenAudioTrack.stop()
      localScreenAudioTrack.detach()
      setLocalScreenAudioTrack(null)
    }

    if (screenStream) {
      const tracks = screenStream.getTracks()
      tracks.forEach((track) => track.stop())
      setScreenStream(null)
    }

    if (livekitRoom) {
      livekitRoom.disconnect()
      setLivekitRoom(null)
      setLivekitConnected(false)
    }

    livekitRoomRef.current = null
    console.log("🧹 LiveKit resources cleaned up")
  }

  const cleanupAllResources = () => {
    cleanupLiveKitResources()

    if (localScreenTrack) {
      localScreenTrack.stop()
      setLocalScreenTrack(null)
    }

    if (localScreenAudioTrack) {
      localScreenAudioTrack.stop()
      setLocalScreenAudioTrack(null)
    }

    if (screenStream) {
      const tracks = screenStream.getTracks()
      tracks.forEach((track) => track.stop())
      setScreenStream(null)
    }

    setIsScreenSharing(false)
    setParticipants([])
  }

  const startScreenShare = async () => {
    try {
      console.log("🖥️ Starting screen share...")
      setStreamStatus("Starting screen share...")

      // First try to get screen share using native getDisplayMedia API
      let screenMediaStream
      try {
        screenMediaStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1920 },
            height: { ideal: 1080 },
            frameRate: { ideal: 30 },
          },
          audio: true,
        })

        console.log("✅ Screen media stream obtained:", screenMediaStream)
        setScreenStream(screenMediaStream)
      } catch (displayErr) {
        console.error("❌ Failed to get display media:", displayErr)
        throw displayErr
      }

      // Create LiveKit screen tracks from the media stream
      const screenTracks = await createLocalScreenTracks({
        audio: true,
        video: true,
        resolution: { width: 1920, height: 1080 },
        frameRate: 30,
      })

      console.log("✅ LiveKit screen tracks created:", screenTracks)

      // Find video and audio tracks
      const screenVideoTrack = screenTracks.find((track) => track.kind === "video")
      const screenAudioTrack = screenTracks.find((track) => track.kind === "audio")

      if (screenVideoTrack) {
        setLocalScreenTrack(screenVideoTrack)
        // Store audio track if available
        if (screenAudioTrack) {
          setLocalScreenAudioTrack(screenAudioTrack)
        }

        // Attach screen video track to video element
        if (screenVideoRef.current) {
          try {
            // Detach any existing tracks first
            if (screenVideoRef.current.srcObject) {
              const existingTracks = screenVideoRef.current.srcObject.getTracks()
              existingTracks.forEach((track) => track.stop())
            }

            // Attach the new screen track
            screenVideoTrack.attach(screenVideoRef.current)
            console.log("✅ Screen video track attached to video element")

            // Also set srcObject directly as fallback
            screenVideoRef.current.srcObject = screenMediaStream
          } catch (attachErr) {
            console.warn("⚠️ Track attach failed, using srcObject:", attachErr)
            screenVideoRef.current.srcObject = screenMediaStream
          }
        }

        // Handle screen share end event
        const videoTrack = screenMediaStream.getVideoTracks()[0]
        if (videoTrack) {
          videoTrack.addEventListener("ended", () => {
            console.log("🖥️ Screen share ended by user")
            stopScreenShare()
          })
        }

        setIsScreenSharing(true)
        setStreamStatus("Screen sharing active")

        // Ensure parallel streaming is active
        setTimeout(() => {
          ensureParallelStreaming()
        }, 1000)

        // Move camera to PiP when screen sharing starts
        if (previewVideoTrack && pipCameraRef.current) {
          try {
            // First detach from main video element if attached
            if (videoRef.current) {
              try {
                previewVideoTrack.detach(videoRef.current)
                console.log("✅ Camera detached from main video")
              } catch (detachErr) {
                console.warn("⚠️ Failed to detach camera from main video:", detachErr)
              }
            }

            // Clear any existing srcObject on PiP element
            if (pipCameraRef.current.srcObject) {
              pipCameraRef.current.srcObject = null
            }

            // Attach to PiP element
            previewVideoTrack.attach(pipCameraRef.current)
            console.log("✅ Camera attached to PiP")

            // Ensure PiP video plays
            pipCameraRef.current.play().catch((playErr) => {
              console.warn("⚠️ PiP camera autoplay failed:", playErr)
            })
          } catch (pipErr) {
            console.warn("⚠️ Failed to attach camera to PiP:", pipErr)
            // Fallback: try to create a new MediaStream from the track
            try {
              const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack])
              pipCameraRef.current.srcObject = mediaStream
              console.log("✅ Camera attached to PiP via MediaStream fallback")
            } catch (fallbackErr) {
              console.error("❌ Camera PiP fallback failed:", fallbackErr)
            }
          }
        }

        // Publish tracks to LiveKit room if connected
        if (livekitRoom && livekitConnected) {
          try {
            // Publish screen video track
            if (screenVideoTrack) {
              await livekitRoom.localParticipant.publishTrack(screenVideoTrack, {
                source: "screen_share",
                name: "teacher_screen",
              })
              console.log("✅ Screen video track published to LiveKit with screen_share source")
            }

            // Publish screen audio track
            if (screenAudioTrack) {
              await livekitRoom.localParticipant.publishTrack(screenAudioTrack, {
                source: "screen_share_audio",
                name: "teacher_screen_audio",
              })
              console.log("✅ Screen audio track published to LiveKit with screen_share_audio source")
            }

            // Ensure camera track is also published/maintained during screen sharing
            const cameraTrack = localVideoTrack || previewVideoTrack
            if (cameraTrack) {
              // Check if camera track is already published with null safety
              const videoTracks = livekitRoom.localParticipant.videoTracks
              const existingCameraPublication =
                videoTracks && videoTracks.values
                  ? Array.from(videoTracks.values()).find(
                      (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
                    )
                  : null

              if (!existingCameraPublication) {
                await livekitRoom.localParticipant.publishTrack(cameraTrack, {
                  source: "camera",
                  name: "teacher_camera",
                })
                console.log("✅ Camera track published alongside screen share")
              } else {
                console.log("ℹ️ Camera track already published, maintaining parallel streaming")
              }
            }
          } catch (publishErr) {
            console.warn("⚠️ Failed to publish screen tracks to LiveKit:", publishErr)
          }
        }
      } else {
        throw new Error("No screen video track found")
      }
    } catch (err) {
      console.error("❌ Error starting screen share:", err)
      let errorMessage = "Failed to start screen share"
      if (err.name === "NotAllowedError") {
        errorMessage = "Screen share permission denied. Please allow screen sharing."
      } else if (err.name === "NotSupportedError") {
        errorMessage = "Screen sharing not supported in this browser."
      } else if (err.name === "NotReadableError") {
        errorMessage = "Screen capture device is busy or unavailable."
      } else if (err.message) {
        errorMessage = err.message
      }
      setStreamStatus(errorMessage)
      dispatch(setError(errorMessage))
      // Cleanup on error
      setIsScreenSharing(false)
      setLocalScreenTrack(null)
      setLocalScreenAudioTrack(null)
      setScreenStream(null)
    }
  }

  const stopScreenShare = async () => {
    try {
      console.log("🛑 Stopping screen share...")
      setStreamStatus("Stopping screen share...")

      // Unpublish screen tracks from LiveKit room if connected
      if (livekitRoom && livekitConnected) {
        try {
          if (localScreenTrack) {
            await livekitRoom.localParticipant.unpublishTrack(localScreenTrack)
            console.log("✅ Screen video track unpublished from LiveKit")
          }

          if (localScreenAudioTrack) {
            await livekitRoom.localParticipant.unpublishTrack(localScreenAudioTrack)
            console.log("✅ Screen audio track unpublished from LiveKit")
          }

          // Ensure camera track remains published after screen sharing stops
          const cameraTrack = localVideoTrack || previewVideoTrack
          if (cameraTrack) {
            const videoTracks = livekitRoom.localParticipant.videoTracks
            const existingCameraPublication =
              videoTracks && videoTracks.values
                ? Array.from(videoTracks.values()).find(
                    (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
                  )
                : null

            if (!existingCameraPublication) {
              await livekitRoom.localParticipant.publishTrack(cameraTrack, {
                source: "camera",
                name: "teacher_camera",
              })
              console.log("✅ Camera track re-published after screen share stopped")
            } else {
              console.log("ℹ️ Camera track still published after screen share stopped")
            }
          }
        } catch (unpublishErr) {
          console.warn("⚠️ Failed to unpublish screen tracks:", unpublishErr)
        }
      }

      // Stop native screen stream
      if (screenStream) {
        const tracks = screenStream.getTracks()
        tracks.forEach((track) => {
          track.stop()
          console.log("✅ Native screen track stopped:", track.kind)
        })
        setScreenStream(null)
      }

      // Stop and cleanup LiveKit screen tracks
      if (localScreenTrack) {
        try {
          localScreenTrack.stop()
          localScreenTrack.detach()
        } catch (detachErr) {
          console.warn("⚠️ Error detaching screen track:", detachErr)
        }
        setLocalScreenTrack(null)
      }

      if (localScreenAudioTrack) {
        try {
          localScreenAudioTrack.stop()
          localScreenAudioTrack.detach()
        } catch (detachErr) {
          console.warn("⚠️ Error detaching screen audio track:", detachErr)
        }
        setLocalScreenAudioTrack(null)
      }

      // Clear screen video element
      if (screenVideoRef.current) {
        screenVideoRef.current.srcObject = null
      }

      setIsScreenSharing(false)
      setStreamStatus("Screen sharing stopped")

      // Ensure camera streaming continues after screen share stops
      setTimeout(() => {
        ensureParallelStreaming()
      }, 1000)

      // Reattach camera to main video element
      if (previewVideoTrack && videoRef.current) {
        try {
          // First detach from PiP element if attached
          if (pipCameraRef.current) {
            try {
              previewVideoTrack.detach(pipCameraRef.current)
              pipCameraRef.current.srcObject = null
              console.log("✅ Camera detached from PiP")
            } catch (detachErr) {
              console.warn("⚠️ Failed to detach camera from PiP:", detachErr)
            }
          }

          // Clear any existing srcObject on main video element
          if (videoRef.current.srcObject) {
            videoRef.current.srcObject = null
          }

          // Attach to main video element
          previewVideoTrack.attach(videoRef.current)
          console.log("✅ Camera reattached to main video")

          // Ensure main video plays
          videoRef.current.play().catch((playErr) => {
            console.warn("⚠️ Main camera autoplay failed:", playErr)
          })
        } catch (reattachErr) {
          console.warn("⚠️ Failed to reattach camera:", reattachErr)
          // Fallback: try to create a new MediaStream from the track
          try {
            const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack])
            videoRef.current.srcObject = mediaStream
            console.log("✅ Camera reattached to main video via MediaStream fallback")
          } catch (fallbackErr) {
            console.error("❌ Camera reattach fallback failed:", fallbackErr)
          }
        }
      }
    } catch (err) {
      console.error("❌ Error stopping screen share:", err)
      setStreamStatus("Error stopping screen share")
      dispatch(setError(err.message || "Failed to stop screen share"))
      // Force cleanup on error
      setIsScreenSharing(false)
      setLocalScreenTrack(null)
      setLocalScreenAudioTrack(null)
      setScreenStream(null)
    }
  }

  // Simple test screen share function (bypasses LiveKit for testing)
  const startTestScreenShare = async () => {
    try {
      console.log("🧪 Starting test screen share...")
      setStreamStatus("Starting test screen share...")

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 },
        },
        audio: true,
      })

      console.log("✅ Test screen stream obtained:", stream)
      setScreenStream(stream)
      setTestScreenShare(true)
      setIsScreenSharing(true)
      setStreamStatus("Test screen sharing active")

      if (screenVideoRef.current) {
        screenVideoRef.current.srcObject = stream
        console.log("✅ Test screen stream attached to video element")
      }

      // Handle stream end
      const videoTrack = stream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.addEventListener("ended", () => {
          console.log("🧪 Test screen share ended")
          stopTestScreenShare()
        })
      }
    } catch (err) {
      console.error("❌ Test screen share failed:", err)
      setStreamStatus("Test screen share failed: " + err.message)
    }
  }

  const stopTestScreenShare = () => {
    console.log("🛑 Stopping test screen share...")

    if (screenStream) {
      const tracks = screenStream.getTracks()
      tracks.forEach((track) => track.stop())
      setScreenStream(null)
    }

    if (screenVideoRef.current) {
      screenVideoRef.current.srcObject = null
    }

    setTestScreenShare(false)
    setIsScreenSharing(false)
    setStreamStatus("Test screen sharing stopped")
  }

  // Quiz Functions
  const handlePdfUpload = (event) => {
    const file = event.target.files[0]
    if (file && file.type === 'application/pdf') {
      setSelectedPdfFile(file)
    } else {
      alert('Please select a valid PDF file')
    }
  }

  const uploadPdfAndGenerateQuestions = async () => {
    if (!selectedPdfFile) {
      alert('Please select a PDF file first')
      return
    }

    try {
      setIsUploadingPdf(true)
      dispatch(setIsQuizLoading(true))

      const formData = new FormData()
      formData.append('pdf', selectedPdfFile)

      const response = await uploadQuizPdf(formData).unwrap()

      if (response.object_id && response.questions) {
        dispatch(setQuizObjectId(response.object_id))
        dispatch(setQuizData(response))
        setShowQuizPanel(true)
        setStreamStatus(`Quiz generated successfully! ${response.questions.length} questions ready.`)
      } else {
        throw new Error('Invalid response from server')
      }
    } catch (error) {
      console.error('❌ PDF upload failed:', error)
      dispatch(setQuizError(error.message || 'Failed to upload PDF'))
      setStreamStatus('PDF upload failed: ' + (error.message || 'Unknown error'))
    } finally {
      setIsUploadingPdf(false)
      dispatch(setIsQuizLoading(false))
    }
  }

  const startQuiz = async () => {
    if (!quizObjectId) {
      alert('No quiz data available. Please upload a PDF first.')
      return
    }

    try {
      dispatch(setIsQuizLoading(true))

      const response = await startQuizMutation({
        quiz_id: quizObjectId
      }).unwrap()

      dispatch(setIsQuizActive(true))
      dispatch(setCurrentQuestionIndex(0))
      dispatch(setQuestionTimer(10))

      // Start the timer for the first question
      startQuestionTimer()

      setStreamStatus('Quiz started! Question 1 is now active.')
    } catch (error) {
      console.error('❌ Failed to start quiz:', error)
      dispatch(setQuizError(error.message || 'Failed to start quiz'))
      setStreamStatus('Failed to start quiz: ' + (error.message || 'Unknown error'))
    } finally {
      dispatch(setIsQuizLoading(false))
    }
  }

  const startQuestionTimer = () => {
    // Clear any existing timer
    if (timerInterval) {
      clearInterval(timerInterval)
    }

    let timeLeft = 10
    dispatch(setQuestionTimer(timeLeft))

    const interval = setInterval(() => {
      timeLeft -= 1
      dispatch(setQuestionTimer(timeLeft))

      if (timeLeft <= 0) {
        clearInterval(interval)
        setTimerInterval(null)
        // Automatically move to next question
        handleNextQuestion()
      }
    }, 1000)

    setTimerInterval(interval)
  }

  const handleNextQuestion = async () => {
    if (!quizData || !quizData.questions) return

    const nextIndex = currentQuestionIndex + 1

    if (nextIndex < quizData.questions.length) {
      try {
        // Call next question API
        await nextQuestionMutation({
          quiz_id: quizObjectId,
          current_question: currentQuestionIndex + 1
        }).unwrap()

        dispatch(setCurrentQuestionIndex(nextIndex))
        dispatch(setQuestionTimer(10))

        // Start timer for next question
        startQuestionTimer()

        setStreamStatus(`Question ${nextIndex + 1} is now active.`)
      } catch (error) {
        console.error('❌ Failed to move to next question:', error)
        setStreamStatus('Failed to move to next question: ' + (error.message || 'Unknown error'))
      }
    } else {
      // Quiz completed
      endQuiz()
    }
  }

  const endQuiz = () => {
    // Clear timer
    if (timerInterval) {
      clearInterval(timerInterval)
      setTimerInterval(null)
    }

    dispatch(setIsQuizActive(false))
    dispatch(setQuestionTimer(0))
    setStreamStatus('Quiz completed! All questions have been answered.')
  }

  const resetQuizState = () => {
    // Clear timer
    if (timerInterval) {
      clearInterval(timerInterval)
      setTimerInterval(null)
    }

    dispatch(resetQuiz())
    setSelectedPdfFile(null)
    setShowQuizPanel(false)
    setStreamStatus('Quiz reset successfully.')
  }

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (timerInterval) {
        clearInterval(timerInterval)
      }
    }
  }, [timerInterval])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  Live Streaming Studio
                </h1>
                <p className="text-gray-600 text-sm">Professional teaching platform</p>
              </div>
            </div>

            {isStreaming && (
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <select
                  value={quality}
                  onChange={(e) => setQuality(e.target.value)}
                  className="px-4 py-2 bg-white/70 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                >
                  <option value="low">Low Quality</option>
                  <option value="medium">Medium Quality</option>
                  <option value="high">High Quality</option>
                </select>
                <button
                  onClick={stopStreaming}
                  className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 font-medium"
                >
                  <span className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                    <span>Stop Streaming</span>
                  </span>
                </button>
              </div>
            )}
          </div>

          {/* Status Banner */}
          {isStreaming && (
            <div className="mt-4 p-4 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
                  <span className="text-emerald-800 font-medium">LIVE</span>
                </div>
                <div className="h-4 w-px bg-emerald-300"></div>
                <p className="text-emerald-700 text-sm font-medium">{streamStatus}</p>
              </div>
            </div>
          )}
        </div>

        {isStreaming ? (
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Main Video Area */}
            <div className="xl:col-span-3">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                <div className="bg-gradient-to-r from-gray-900 to-black relative" style={{ aspectRatio: "16/9" }}>
                  {!isScreenSharing ? (
                    <div className="flex items-center justify-center h-full text-white">
                      <div className="text-center space-y-6 p-8">
                        <div className="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto">
                          <svg className="w-10 h-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold mb-2">Ready to Share Your Screen</h3>
                          <p className="text-gray-300 mb-6">Choose your preferred screen sharing method</p>
                        </div>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                          <button
                            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 font-medium"
                            onClick={startScreenShare}
                          >
                            <span className="flex items-center space-x-2">
                              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                />
                              </svg>
                              <span>Start Screen Share</span>
                            </span>
                          </button>
                          <button
                            className="px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl hover:from-emerald-700 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 font-medium"
                            onClick={startTestScreenShare}
                          >
                            <span className="flex items-center space-x-2">
                              <span>🧪</span>
                              <span>Test Mode</span>
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* Screen Share Video */}
                      <video
                        ref={screenVideoRef}
                        autoPlay
                        playsInline
                        muted={false}
                        className="w-full h-full object-contain bg-black"
                        onLoadedMetadata={() => console.log("✅ Screen video metadata loaded")}
                        onCanPlay={() => console.log("✅ Screen video can play")}
                        onError={(e) => console.error("❌ Screen video error:", e)}
                      />

                      {/* Screen Share Status Overlay */}
                      {!localScreenTrack && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                          <div className="text-white text-center">
                            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
                            <p className="text-lg font-medium">Loading screen share...</p>
                          </div>
                        </div>
                      )}

                      {/* Picture-in-Picture Camera */}
                      <div className="absolute bottom-6 right-6 w-56 h-40 bg-black rounded-2xl overflow-hidden border-4 border-white/20 shadow-2xl transition-all duration-300 hover:scale-105 hover:border-white/40">
                        <video
                          ref={pipCameraRef}
                          autoPlay
                          playsInline
                          muted
                          className="w-full h-full object-cover"
                          onLoadedMetadata={() => console.log("✅ PiP camera metadata loaded")}
                          onCanPlay={() => console.log("✅ PiP camera can play")}
                          onPlay={() => console.log("✅ PiP camera started playing")}
                          onError={(e) => console.error("❌ PiP camera error:", e)}
                          onLoadStart={() => console.log("🎥 PiP camera load started")}
                        />

                        {/* PiP Status Indicators */}
                        <div className="absolute top-2 left-2 flex items-center space-x-2">
                          <div className="px-2 py-1 bg-black/70 backdrop-blur-sm text-white text-xs rounded-lg font-medium">
                            <span className="flex items-center space-x-1">
                              <div
                                className={`w-2 h-2 rounded-full ${previewVideoTrack ? "bg-emerald-400" : "bg-red-400"}`}
                              ></div>
                              <span>Camera</span>
                            </span>
                          </div>
                        </div>

                        {/* PiP Refresh Button */}
                        <button
                          onClick={async () => {
                            try {
                              console.log("🔄 Manually refreshing PiP camera...")
                              if (previewVideoTrack && pipCameraRef.current) {
                                pipCameraRef.current.srcObject = null
                                previewVideoTrack.attach(pipCameraRef.current)
                                pipCameraRef.current.play()
                                console.log("✅ PiP camera manually refreshed")
                              } else {
                                await refreshCameraTrack()
                                console.log("✅ PiP camera refreshed with new track")
                              }
                            } catch (err) {
                              console.error("❌ Manual PiP refresh failed:", err)
                              if (previewVideoTrack && pipCameraRef.current) {
                                try {
                                  const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack])
                                  pipCameraRef.current.srcObject = mediaStream
                                  console.log("✅ PiP camera refreshed via fallback")
                                } catch (fallbackErr) {
                                  console.error("❌ PiP fallback refresh failed:", fallbackErr)
                                }
                              }
                            }
                          }}
                          className="absolute top-2 right-2 w-8 h-8 bg-blue-600/80 hover:bg-blue-600 text-white rounded-lg transition-all duration-200 flex items-center justify-center text-sm hover:scale-110"
                          title="Refresh PiP Camera"
                        >
                          🔄
                        </button>
                      </div>

                      {/* Screen Share Controls */}
                      <div className="absolute top-6 left-6 flex items-center space-x-3">
                        <button
                          onClick={testScreenShare ? stopTestScreenShare : stopScreenShare}
                          className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 font-medium"
                        >
                          <span className="flex items-center space-x-2">
                            <span>🛑</span>
                            <span>Stop Screen Share</span>
                          </span>
                        </button>
                        {testScreenShare && (
                          <div className="px-4 py-2 bg-emerald-500 text-white rounded-xl text-sm font-medium">
                            🧪 Test Mode Active
                          </div>
                        )}
                      </div>

                      {/* Debug Info Panel */}
                      <div className="absolute top-6 right-6 bg-black/80 backdrop-blur-sm text-white text-xs p-4 rounded-xl border border-white/20">
                        <div className="space-y-1">
                          <div className="flex items-center justify-between space-x-4">
                            <span>Screen Track:</span>
                            <span className={localScreenTrack ? "text-emerald-400" : "text-red-400"}>
                              {localScreenTrack ? "✅" : "❌"}
                            </span>
                          </div>
                          <div className="flex items-center justify-between space-x-4">
                            <span>Screen Stream:</span>
                            <span className={screenStream ? "text-emerald-400" : "text-red-400"}>
                              {screenStream ? "✅" : "❌"}
                            </span>
                          </div>
                          <div className="flex items-center justify-between space-x-4">
                            <span>Camera Track:</span>
                            <span className={previewVideoTrack ? "text-emerald-400" : "text-red-400"}>
                              {previewVideoTrack ? "✅" : "❌"}
                            </span>
                          </div>
                          <div className="flex items-center justify-between space-x-4">
                            <span>Parallel Mode:</span>
                            <span
                              className={localScreenTrack && previewVideoTrack ? "text-emerald-400" : "text-yellow-400"}
                            >
                              {localScreenTrack && previewVideoTrack ? "✅ Active" : "⚠️ Inactive"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Floating Chat Button */}
                      {!isChatOpen && (
                        <button
                          onClick={toggleChat}
                          className="absolute bottom-6 left-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white p-4 rounded-2xl shadow-2xl transition-all duration-200 transform hover:scale-110"
                        >
                          <span className="flex items-center space-x-2">
                            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                              />
                            </svg>
                            {unreadMessages > 0 && (
                              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold animate-bounce">
                                {unreadMessages}
                              </span>
                            )}
                          </span>
                        </button>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="xl:col-span-1 space-y-6">
              {/* Camera Preview */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800">
                    {isScreenSharing ? "Camera (PiP Mode)" : "Camera Preview"}
                  </h3>
                  <div
                    className={`w-3 h-3 rounded-full ${cameraPermissionGranted ? "bg-emerald-500 animate-pulse" : "bg-gray-400"}`}
                  ></div>
                </div>
                <div
                  className="bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden"
                  style={{ aspectRatio: "4/3" }}
                >
                  <video ref={videoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
                </div>
              </div>

              {/* Stream Statistics */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
                  <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  <span>Stream Status</span>
                </h3>

                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${livekitConnected ? "bg-emerald-500 animate-pulse" : "bg-gray-400"}`}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">LiveKit</span>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${livekitConnected ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"}`}
                    >
                      {livekitConnected ? "Connected" : "Disconnected"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${cameraPermissionGranted ? "bg-emerald-500 animate-pulse" : "bg-gray-400"}`}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">Camera</span>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${cameraPermissionGranted ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"}`}
                    >
                      {cameraPermissionGranted ? "Ready" : "Not Ready"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${isScreenSharing ? "bg-emerald-500 animate-pulse" : "bg-gray-400"}`}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">Screen Share</span>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${isScreenSharing ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"}`}
                    >
                      {isScreenSharing ? "Active" : "Inactive"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${socketConnected ? "bg-emerald-500 animate-pulse" : "bg-gray-400"}`}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">Chat</span>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${socketConnected ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"}`}
                    >
                      {socketConnected ? "Connected" : "Disconnected"}
                    </span>
                  </div>
                </div>

                {/* Session Info */}
                <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-xl">
                  <div className="text-xs text-yellow-800 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Session ID:</span>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(sessionId)
                          alert("Session ID copied to clipboard!")
                        }}
                        className="text-yellow-600 hover:text-yellow-800 transition-colors"
                        title="Copy Session ID"
                      >
                        📋
                      </button>
                    </div>
                    <div className="bg-yellow-100 p-2 rounded text-xs font-mono break-all">
                      {sessionId || "Not started"}
                    </div>
                    <input
                      type="text"
                      placeholder="Override session ID for testing"
                      className="w-full text-xs p-2 bg-yellow-50 text-yellow-800 border border-yellow-300 rounded focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && e.target.value.trim()) {
                          const newSessionId = e.target.value.trim()
                          console.log(`🔧 TEACHER: Overriding session ID to: ${newSessionId}`)
                          setSessionId(newSessionId)
                          e.target.value = ""
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Debug Controls */}
                {livekitConnected && (previewVideoTrack || localVideoTrack) && (
                  <div className="mt-4 space-y-2">
                    <button
                      onClick={async () => {
                        try {
                          const track = localVideoTrack || previewVideoTrack
                          if (track && livekitRoom) {
                            await livekitRoom.localParticipant.publishTrack(track, {
                              source: "camera",
                              name: "teacher_camera_debug",
                            })
                            console.log("🔧 Debug: Camera track published manually")
                          }
                        } catch (err) {
                          console.error("🔧 Debug: Failed to publish camera track:", err)
                        }
                      }}
                      className="w-full px-3 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl text-xs hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 font-medium"
                    >
                      🔧 Debug: Publish Camera
                    </button>

                    <button
                      onClick={async () => {
                        try {
                          await refreshCameraTrack()
                          console.log("🔧 Debug: Camera track refreshed manually")
                        } catch (err) {
                          console.error("🔧 Debug: Failed to refresh camera track:", err)
                        }
                      }}
                      className="w-full px-3 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl text-xs hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
                    >
                      🔄 Refresh Camera Track
                    </button>

                    <button
                      onClick={async () => {
                        try {
                          await ensureParallelStreaming()
                          console.log("🔧 Debug: Parallel streaming ensured manually")
                        } catch (err) {
                          console.error("🔧 Debug: Failed to ensure parallel streaming:", err)
                        }
                      }}
                      className="w-full px-3 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-xl text-xs hover:from-purple-600 hover:to-indigo-600 transition-all duration-200 font-medium"
                    >
                      📹 Ensure Parallel Streaming
                    </button>
                  </div>
                )}
              </div>

              {/* Participants */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                    <span>Viewers</span>
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {joinedViewers.length}
                    </span>
                    {!socketConnected && (
                      <button
                        onClick={async () => {
                          try {
                            const workingUrl = await testSocketConnection()
                            console.log("✅ Found working URL:", workingUrl)
                            window.location.reload()
                          } catch (error) {
                            console.error("❌ All connection tests failed:", error)
                            alert("Unable to connect to chat server. Please check your internet connection.")
                          }
                        }}
                        className="px-2 py-1 bg-blue-500 text-white rounded-lg text-xs hover:bg-blue-600 transition-colors"
                        title="Retry Chat Connection"
                      >
                        🔄
                      </button>
                    )}
                  </div>
                </div>

                <div className="space-y-3 max-h-48 overflow-y-auto">
                  {joinedViewers.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg className="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                          />
                        </svg>
                      </div>
                      <p className="text-sm text-gray-500 font-medium">No viewers yet</p>
                      <p className="text-xs text-gray-400 mt-1">Waiting for students to join...</p>
                      {!socketConnected && (
                        <p className="text-xs text-red-500 mt-2">
                          ⚠️ Chat disconnected - viewer tracking may be limited
                        </p>
                      )}
                    </div>
                  ) : (
                    joinedViewers.map((viewer, index) => (
                      <div
                        key={viewer.viewer_id || index}
                        className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-100 hover:border-blue-200 transition-all duration-200"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {viewer.viewer_name?.charAt(0)?.toUpperCase() || "U"}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-800">{viewer.viewer_name || "Unknown User"}</p>
                            <div className="flex items-center space-x-2">
                              <span
                                className={`text-xs px-2 py-1 rounded-full font-medium ${getRoleColor(viewer.user_role)} bg-current bg-opacity-10`}
                              >
                                {getRoleBadge(viewer.user_role)}
                              </span>
                              {viewer.source && (
                                <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                                  {viewer.source === "livekit" ? "📹" : "💬"}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-xs text-gray-400">
                          {new Date(viewer.joined_at).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Chat Section */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                      />
                    </svg>
                    <span>Live Chat</span>
                    {unreadMessages > 0 && !isChatOpen && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-bounce">
                        {unreadMessages}
                      </span>
                    )}
                  </h3>
                  <button
                    onClick={toggleChat}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                  >
                    {isChatOpen ? "Hide" : "Show"}
                  </button>
                </div>

                {isChatOpen && (
                  <div className="space-y-4">
                    {/* Chat Messages */}
                    <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border border-gray-200 h-64 overflow-y-auto p-4">
                      {chatMessages.length === 0 ? (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                              <svg
                                className="w-6 h-6 text-gray-400"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                                />
                              </svg>
                            </div>
                            <p className="text-sm text-gray-500 font-medium">No messages yet</p>
                            <p className="text-xs text-gray-400 mt-1">Start the conversation!</p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {chatMessages.map((message, index) => (
                            <div
                              key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
                              className="bg-white rounded-xl p-3 shadow-sm border border-gray-100"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}>
                                      {message.sender_name}
                                    </span>
                                    <span
                                      className={`text-xs px-2 py-1 rounded-full font-medium ${getRoleColor(message.sender_role)} bg-current bg-opacity-10`}
                                    >
                                      {getRoleBadge(message.sender_role)}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-800 leading-relaxed">{message.message}</p>
                                </div>
                                <span className="text-xs text-gray-400 ml-3 flex-shrink-0">
                                  {formatMessageTime(message.timestamp)}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Chat Input */}
                    <div className="flex space-x-3">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Type your message..."
                        className="flex-1 px-4 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                        disabled={!socketConnected}
                      />
                      <button
                        onClick={sendChatMessage}
                        disabled={!newMessage.trim() || !socketConnected}
                        className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                      >
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Chat Status & Controls */}
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${socketConnected ? "bg-emerald-500 animate-pulse" : "bg-red-500"}`}
                        ></div>
                        <span className="text-gray-600 font-medium">
                          Chat {socketConnected ? "Connected" : "Disconnected"}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={async () => {
                            try {
                              const result = await checkBackendHealth()
                              alert(`✅ Backend is healthy!\nURL: ${result.url}\nService: ${result.data.service}`)
                            } catch (error) {
                              alert(`❌ Backend health check failed: ${error.message}`)
                            }
                          }}
                          className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                          title="Check Backend Health"
                        >
                          🏥
                        </button>
                        <button
                          onClick={testSocketConnection}
                          className="px-3 py-1 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
                          title="Test Socket Connection"
                        >
                          🧪
                        </button>
                        {socketConnected && (
                          <button
                            onClick={sendTestMessage}
                            className="px-3 py-1 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors"
                            title="Send Test Message"
                          >
                            📤
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Quiz Panel */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                    <svg className="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <span>Quiz Generator</span>
                  </h3>
                  {isQuizActive && (
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-green-600">Active</span>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {!showQuizPanel ? (
                    // PDF Upload Section
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-purple-400 transition-colors">
                        <input
                          type="file"
                          accept=".pdf"
                          onChange={handlePdfUpload}
                          className="hidden"
                          id="pdf-upload"
                        />
                        <label htmlFor="pdf-upload" className="cursor-pointer">
                          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg className="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                              />
                            </svg>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {selectedPdfFile ? selectedPdfFile.name : 'Click to upload PDF'}
                          </p>
                          <p className="text-xs text-gray-400">PDF files only</p>
                        </label>
                      </div>

                      {selectedPdfFile && (
                        <button
                          onClick={uploadPdfAndGenerateQuestions}
                          disabled={isUploadingPdf}
                          className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-xl hover:from-purple-700 hover:to-indigo-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isUploadingPdf ? (
                            <span className="flex items-center justify-center space-x-2">
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                              <span>Generating Questions...</span>
                            </span>
                          ) : (
                            'Generate Questions'
                          )}
                        </button>
                      )}
                    </div>
                  ) : (
                    // Quiz Control Section
                    <div className="space-y-4">
                      {quizData && (
                        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 border border-purple-200">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-purple-800">
                              Questions Ready: {quizData.questions?.length || 0}
                            </span>
                            {isQuizActive && (
                              <span className="text-sm font-medium text-green-600">
                                Question {currentQuestionIndex + 1} of {quizData.questions?.length || 0}
                              </span>
                            )}
                          </div>

                          {isQuizActive && questionTimer > 0 && (
                            <div className="mb-3">
                              <div className="flex items-center justify-between text-sm mb-1">
                                <span className="text-gray-600">Time Remaining</span>
                                <span className="font-bold text-red-600">{questionTimer}s</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-1000"
                                  style={{ width: `${(questionTimer / 10) * 100}%` }}
                                ></div>
                              </div>
                            </div>
                          )}

                          {isQuizActive && quizData.questions && quizData.questions[currentQuestionIndex] && (
                            <div className="bg-white rounded-lg p-4 border border-gray-200">
                              <div className="mb-3">
                                <h4 className="font-semibold text-gray-800 mb-2">
                                  Q{currentQuestionIndex + 1}: {quizData.questions[currentQuestionIndex].question_text}
                                </h4>
                                {quizData.questions[currentQuestionIndex].question_image && (
                                  <img
                                    src={quizData.questions[currentQuestionIndex].question_image}
                                    alt="Question"
                                    className="max-w-full h-auto rounded-lg mb-3"
                                  />
                                )}
                              </div>
                              <div className="grid grid-cols-1 gap-2">
                                {quizData.questions[currentQuestionIndex].options?.map((option, index) => (
                                  <div
                                    key={index}
                                    className={`p-2 rounded-lg border text-sm ${
                                      parseInt(quizData.questions[currentQuestionIndex].answer) === index + 1
                                        ? 'bg-green-100 border-green-300 text-green-800'
                                        : 'bg-gray-50 border-gray-200 text-gray-700'
                                    }`}
                                  >
                                    <span className="font-medium">{String.fromCharCode(65 + index)}.</span> {option}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex space-x-2">
                        {!isQuizActive ? (
                          <button
                            onClick={startQuiz}
                            disabled={!quizObjectId || isQuizLoading}
                            className="flex-1 px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isQuizLoading ? (
                              <span className="flex items-center justify-center space-x-2">
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                <span>Starting...</span>
                              </span>
                            ) : (
                              'Start Quiz'
                            )}
                          </button>
                        ) : (
                          <button
                            onClick={handleNextQuestion}
                            disabled={!quizData || currentQuestionIndex >= (quizData.questions?.length || 0) - 1}
                            className="flex-1 px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {currentQuestionIndex >= (quizData.questions?.length || 0) - 1 ? 'Quiz Complete' : 'Next Question'}
                          </button>
                        )}

                        <button
                          onClick={resetQuizState}
                          className="px-4 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 font-medium"
                        >
                          Reset
                        </button>
                      </div>
                    </div>
                  )}

                  {quizError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-700 text-sm">{quizError}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Start Streaming Screen */
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12">
            <div className="text-center max-w-2xl mx-auto">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>

              <h2 className="text-3xl font-bold text-gray-800 mb-4">Ready to Go Live?</h2>
              <p className="text-gray-600 mb-8 text-lg leading-relaxed">{streamStatus}</p>

              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-700 text-sm font-medium">{error}</p>
                </div>
              )}

              <button
                onClick={startStreaming}
                className="px-12 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105 font-semibold text-lg"
              >
                <span className="flex items-center space-x-3">
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M15 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Start Live Streaming</span>
                </span>
              </button>

              <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-800 mb-2">HD Video Quality</h3>
                  <p className="text-sm text-gray-600">Crystal clear video streaming with adaptive quality</p>
                </div>

                <div className="p-4 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl border border-emerald-100">
                  <div className="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-800 mb-2">Screen Sharing</h3>
                  <p className="text-sm text-gray-600">Share your screen with picture-in-picture camera</p>
                </div>

                <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                  <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                      />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-gray-800 mb-2">Live Chat</h3>
                  <p className="text-sm text-gray-600">Real-time interaction with your students</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TeacherLiveStreaming
