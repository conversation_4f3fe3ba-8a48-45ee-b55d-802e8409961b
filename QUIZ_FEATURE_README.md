# Live Streaming Quiz Feature

## Overview
This feature adds interactive quiz functionality to the live streaming platform, allowing teachers to upload PDF questions and conduct real-time quizzes with hand gesture detection for student responses.

## Features

### Teacher Side (TeacherLiveStreaming.jsx)
1. **PDF Upload & Question Generation**
   - Upload PDF files containing quiz questions
   - Automatic question extraction and parsing
   - Preview generated questions before starting quiz

2. **Quiz Management**
   - Start/stop quiz sessions
   - Navigate through questions with 10-second timers
   - Real-time question display with options
   - Automatic progression to next questions

3. **Quiz Controls**
   - Question timer with visual countdown
   - Manual next question button
   - Quiz reset functionality
   - Real-time status updates

### Viewer Side (CenterTraineeLiveViewer.jsx)
1. **Automatic Quiz Participation**
   - Quiz questions automatically appear when teacher starts quiz
   - Full-screen quiz overlay with question and options
   - Real-time timer synchronization

2. **Hand Gesture Detection**
   - Automatic camera activation during quiz
   - Real-time hand gesture analysis via WebSocket
   - Visual feedback for detected hand positions
   - Option selection based on hand gestures

3. **Quiz Interface**
   - Clean, responsive quiz display
   - Visual timer countdown
   - Hand detection status indicators
   - Camera preview window

## Technical Implementation

### Backend Endpoints
- `POST https://sasthra.in/upload_cbt_paper` - Upload PDF and generate questions
- `POST https://sasthra.in/content/start_quiz` - Start quiz session
- `POST https://sasthra.in/content/next_question` - Move to next question

### WebSocket Communication
- `wss://sasthra.in/socketio2` - Hand detection and quiz synchronization
- Real-time frame capture and analysis
- Bidirectional communication for quiz state

### State Management
- Redux-based state management for quiz data
- Separate quiz slice for viewer state
- Real-time synchronization between teacher and viewers

## Usage Instructions

### For Teachers:
1. Start live streaming session
2. Click "Question Generation" in the sidebar
3. Upload a PDF file with quiz questions
4. Click "Generate Questions" to process the PDF
5. Review the generated questions
6. Click "Start Quiz" to begin the quiz session
7. Questions will automatically progress with 10-second timers
8. Use "Next Question" button for manual control
9. Click "Reset" to end the quiz

### For Viewers:
1. Join the live stream as usual
2. When teacher starts quiz, questions will automatically appear
3. Camera will activate automatically for hand detection
4. Raise your hand to indicate your answer choice:
   - Raise hand in different positions for options A, B, C, D
5. Visual feedback will show your detected answer
6. Wait for the next question to appear automatically

## File Structure
```
src/
├── pages/screens/teacherPanel/teacherLiveStreaming/
│   ├── TeacherLiveStreaming.jsx (Updated with quiz functionality)
│   └── teacherLiveStreaming.slice.js (Updated with quiz endpoints)
├── pages/screens/centreTraineePanel/centerTraineeLiveViewer/
│   ├── CenterTraineeLiveViewer.jsx (Updated with quiz UI)
│   ├── centerTraineeLive.slice.js (Updated with quiz endpoints)
│   └── quiz.slice.js (New quiz state management)
└── redux/store/store.js (Updated with quiz reducer)
```

## Key Components

### Quiz State Management
- `quizData`: Complete quiz information from backend
- `isQuizActive`: Boolean indicating if quiz is currently running
- `currentQuestionIndex`: Index of current question being displayed
- `questionTimer`: Countdown timer for current question
- `selectedOption`: User's selected answer option
- `isHandRaised`: Boolean indicating if hand gesture is detected

### WebSocket Messages
- `quiz_started`: Sent when teacher starts quiz
- `next_question`: Sent when moving to next question
- `quiz_ended`: Sent when quiz is completed
- `video_frame`: Sent from viewer camera for hand detection
- `hand_detection_result`: Sent back with detected hand position

## Error Handling
- Camera access permission handling
- WebSocket connection failure recovery
- PDF upload error management
- Quiz state synchronization error handling

## Browser Compatibility
- Requires modern browsers with WebRTC support
- Camera access permissions required for viewers
- WebSocket support required for real-time communication

## Security Considerations
- Camera access only during quiz sessions
- Secure WebSocket connections (WSS)
- PDF file validation and size limits
- User permission management for camera access

## Future Enhancements
- Multiple choice question types
- Quiz result analytics
- Replay functionality
- Custom timer settings
- Advanced hand gesture recognition
- Mobile device optimization
